# Credit Chakra Development Environment Setup

This document provides instructions for setting up and running the complete Credit Chakra development environment, including all three applications: Backend API, Credit Chakra Frontend (Manager Dashboard), and MSME Application (Business Portal).

## Quick Start

### For Unix/Linux/macOS Users

```bash
./run-all-dev.sh
```

### For Windows Users

```cmd
run-all-dev.bat
```

## What Gets Started

When you run the development script, it will start three services simultaneously:

| Service | Port | URL | Description |
|---------|------|-----|-------------|
| **Backend API** | 8000 | http://localhost:8000 | FastAPI backend serving all APIs |
| **Credit Chakra Frontend** | 3000 | http://localhost:3000 | Manager Dashboard (Next.js) |
| **MSME Application** | 3001 | http://localhost:3001 | Business Portal (Next.js) |

## Prerequisites

Before running the development environment, ensure you have the following installed:

### Required Software

1. **Python 3.8+**
   - Download from [python.org](https://www.python.org/downloads/)
   - Verify installation: `python3 --version`

2. **Node.js 18+**
   - Download from [nodejs.org](https://nodejs.org/)
   - Verify installation: `node --version`

3. **npm (comes with Node.js)**
   - Verify installation: `npm --version`

### Port Requirements

The script will automatically check if the following ports are available:
- **Port 8000**: Backend API
- **Port 3000**: Credit Chakra Frontend
- **Port 3001**: MSME Application

If any port is in use, the script will exit with an error message.

## Features of the Development Script

### Automatic Setup
- ✅ Creates Python virtual environment if it doesn't exist
- ✅ Installs backend dependencies automatically
- ✅ Installs frontend dependencies for both applications
- ✅ Checks for port conflicts before starting services

### Process Management
- ✅ Starts all three services in the correct order
- ✅ Provides clear status updates and URLs
- ✅ Handles graceful shutdown with Ctrl+C (Unix) or closing windows (Windows)

### Error Handling
- ✅ Validates prerequisites before starting
- ✅ Stops all services if any service fails to start
- ✅ Provides clear error messages

## Demo Login Credentials

### Credit Manager Dashboard (Port 3000)
- **Email**: <EMAIL>
- **Password**: manager123

### MSME Business Portal (Port 3001)
- **Email**: <EMAIL>
- **Password**: demo123

## Manual Setup (Alternative)

If you prefer to start services manually or need to debug issues:

### 1. Start Backend (Terminal 1)
```bash
cd backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

### 2. Start Credit Chakra Frontend (Terminal 2)
```bash
cd frontend
npm install
npm run dev
```

### 3. Start MSME Application (Terminal 3)
```bash
cd msme-app
npm install
npm run dev -- --port 3001
```

## Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Check what's using the port: `lsof -i :PORT_NUMBER` (Unix) or `netstat -an | find ":PORT"` (Windows)
   - Kill the process or change the port in the respective configuration

2. **Python Virtual Environment Issues**
   - Delete the `backend/venv` folder and run the script again
   - Ensure Python 3.8+ is installed

3. **Node.js Dependency Issues**
   - Delete `node_modules` folders in `frontend/` and `msme-app/`
   - Run the script again to reinstall dependencies

4. **Permission Issues (Unix/Linux/macOS)**
   - Make sure the script is executable: `chmod +x run-all-dev.sh`

### Getting Help

If you encounter issues:

1. Check the terminal output for specific error messages
2. Ensure all prerequisites are properly installed
3. Try the manual setup process to isolate the issue
4. Check the individual application README files for specific requirements

## Development Workflow

1. **Start Development Environment**
   ```bash
   ./run-all-dev.sh  # or run-all-dev.bat on Windows
   ```

2. **Access Applications**
   - Open http://localhost:3000 for Credit Manager Dashboard
   - Open http://localhost:3001 for MSME Business Portal
   - Open http://localhost:8000/docs for API documentation

3. **Make Changes**
   - Both frontend applications support hot reload
   - Backend changes require manual restart

4. **Stop Development Environment**
   - Press `Ctrl+C` in the terminal (Unix/Linux/macOS)
   - Close the individual command windows (Windows)

## API Documentation

Once the backend is running, you can access the interactive API documentation at:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Next Steps

After setting up the development environment:

1. Explore the Credit Manager Dashboard at http://localhost:3000
2. Test the MSME Business Portal at http://localhost:3001
3. Review the API documentation at http://localhost:8000/docs
4. Check the individual application README files for specific development guidelines
