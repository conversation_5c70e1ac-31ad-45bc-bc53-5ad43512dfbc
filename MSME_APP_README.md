# Credit Chakra MSME Application

A comprehensive MSME-facing web application built with Next.js and FastAPI, providing credit scoring, behavioral nudges, training modules, and government scheme navigation.

## 🚀 Quick Start

### Prerequisites

- **Python 3.8+** with pip
- **Node.js 18+** with npm
- **Git** (for version control)

### One-Command Setup

#### For macOS/Linux:
```bash
./run-dev.sh
```

#### For Windows:
```batch
run-dev.bat
```

### Manual Setup

#### 1. Backend Setup
```bash
cd backend
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

#### 2. Frontend Setup
```bash
cd msme-app
npm install
npm run dev
```

## 📱 Application Access

- **Frontend (MSME App)**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## 🔐 Demo Credentials

```
Email: <EMAIL>
Password: demo123
```

## 🏗️ Application Architecture

### Frontend (Next.js)
- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS + ShadCN UI
- **State Management**: React hooks + localStorage
- **Charts**: Recharts for data visualization
- **Icons**: Lucide React

### Backend (FastAPI)
- **Framework**: FastAPI with Python
- **Database**: Firebase Firestore
- **Authentication**: JWT-based
- **File Generation**: ReportLab (PDF), QRCode
- **API Documentation**: Automatic OpenAPI/Swagger

## 📋 Features

### 🎯 CCR Score Dashboard
- **0-100 Credit Score** with risk band classification
- **5-Component Breakdown**: GST Compliance, UPI Diversity, Digital Presence, Financial Health, Business Stability
- **Interactive Charts**: Trends analysis and score visualization
- **Export Functionality**: PDF reports and QR codes for verification
- **Personalized Recommendations**: AI-driven suggestions for score improvement

### 🔔 Nudges & Reminders
- **Behavioral Nudges**: Personalized action items based on business profile
- **Category-based Organization**: GST compliance, UPI usage, financial health, business growth
- **Priority System**: High, medium, and low priority nudges
- **Engagement Tracking**: Monitor action completion rates
- **Real-time Notifications**: In-app notifications for urgent actions

### 📚 Training & Micro-Advisory
- **Course Discovery**: Browse courses by category, difficulty, and business type
- **Progress Tracking**: Monitor learning progress and completion rates
- **Interactive Content**: SCORM-compatible content delivery
- **Certification System**: Earn certificates upon course completion
- **Personalized Learning Path**: Recommendations based on business needs

### 🏛️ Scheme Navigator
- **Government Scheme Discovery**: Browse 100+ government schemes
- **Eligibility Checking**: AI-powered eligibility assessment
- **Smart Filtering**: Filter by scheme type, business type, location
- **Bookmark System**: Save schemes for later review
- **Application Tracking**: Monitor application status and progress

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new MSME user
- `POST /api/auth/login` - User login
- `GET /api/auth/profile` - Get user profile

### CCR Score
- `GET /api/ccr/{msme_id}/score` - Get CCR score with breakdown
- `GET /api/ccr/{msme_id}/ccr-trends` - Get score trends
- `POST /api/ccr/{msme_id}/export` - Export score as PDF/QR
- `GET /api/ccr/{msme_id}/download/{export_id}` - Download exported file

### Behavioral Nudges
- `GET /nudges/behavioral` - Get behavioral nudges
- `POST /nudges/behavioral` - Create personalized nudge
- `PATCH /nudges/behavioral/{nudge_id}/read` - Mark nudge as read

### Training
- `GET /api/training/courses` - Get available courses
- `POST /api/training/courses/{course_id}/enroll` - Enroll in course
- `GET /api/training/courses/{course_id}/progress` - Get progress

### Schemes
- `GET /api/schemes/schemes` - Get government schemes
- `POST /api/schemes/schemes/{scheme_id}/eligibility` - Check eligibility
- `POST /api/schemes/schemes/{scheme_id}/bookmark` - Bookmark scheme

## 🎨 UI/UX Features

### Design System
- **Color Palette**: Emerald-based theme for trust and growth
- **Typography**: Clean, readable fonts optimized for business users
- **Responsive Design**: Mobile-first approach with desktop optimization
- **Accessibility**: WCAG 2.1 compliant components

### User Experience
- **Intuitive Navigation**: Clear sidebar navigation with active states
- **Progressive Disclosure**: Information revealed progressively to avoid overwhelm
- **Contextual Help**: Tooltips and help text throughout the application
- **Loading States**: Smooth loading indicators and skeleton screens

## 📊 Data Models

### MSME Profile
```typescript
interface MSMEProfile {
  msme_id: string;
  name: string;
  business_type: 'retail' | 'b2b' | 'services' | 'manufacturing';
  location: string;
  ccr_score?: number;
  risk_band?: 'green' | 'yellow' | 'red';
}
```

### CCR Score Breakdown
```typescript
interface CCRScoreBreakdown {
  gst_compliance: number;
  upi_diversity: number;
  digital_presence: number;
  financial_health: number;
  business_stability: number;
}
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive input sanitization
- **CORS Configuration**: Proper cross-origin resource sharing setup
- **Environment Variables**: Sensitive data stored in environment variables

## 🚀 Deployment

### Development
```bash
# Start both servers
./run-dev.sh  # macOS/Linux
run-dev.bat   # Windows
```

### Production
```bash
# Backend
cd backend
gunicorn main:app --host 0.0.0.0 --port 8000

# Frontend
cd msme-app
npm run build
npm start
```

## 📈 Performance Optimizations

- **Code Splitting**: Automatic code splitting with Next.js
- **Image Optimization**: Next.js Image component for optimized loading
- **API Caching**: Strategic caching of API responses
- **Bundle Analysis**: Regular bundle size monitoring

## 🧪 Testing

```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd msme-app
npm test
```

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Documentation**: http://localhost:8000/docs
- **Issues**: GitHub Issues

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Built with ❤️ for MSMEs in India**
