/**
 * API client for MSME-facing application
 * Handles communication with Credit Chakra backend
 */

import axios from 'axios';

// API base URL - can be configured via environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

// Create axios instance with default configuration
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('msme_auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('msme_auth_token');
      window.location.href = '/auth/login';
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const api = {
  // Authentication
  auth: {
    register: (data: any) => apiClient.post('/api/auth/register', data),
    login: (data: any) => apiClient.post('/api/auth/login', data),
    logout: () => apiClient.post('/api/auth/logout'),
    profile: () => apiClient.get('/api/auth/profile'),
    updateProfile: (data: any) => apiClient.put('/api/auth/profile', data),
  },

  // CCR Score
  ccr: {
    getScore: (msmeId: string) => apiClient.get(`/api/ccr/${msmeId}/score`),
    getTrends: (msmeId: string, months?: number) => 
      apiClient.get(`/api/ccr/${msmeId}/ccr-trends`, { params: { months } }),
    exportScore: (msmeId: string, format: 'pdf' | 'qr') => 
      apiClient.post(`/api/ccr/${msmeId}/export`, { format }),
    downloadExport: (msmeId: string, exportId: string) => 
      apiClient.get(`/api/ccr/${msmeId}/download/${exportId}`, { responseType: 'blob' }),
  },

  // Behavioral Nudges
  nudges: {
    getBehavioralNudges: (params?: any) => 
      apiClient.get('/nudges/behavioral', { params }),
    createBehavioralNudge: (data: any) => 
      apiClient.post('/nudges/behavioral', data),
    markAsRead: (nudgeId: string) => 
      apiClient.patch(`/nudges/behavioral/${nudgeId}/read`),
  },

  // Training
  training: {
    getCourses: (params?: any) => 
      apiClient.get('/api/training/courses', { params }),
    enrollCourse: (courseId: string) => 
      apiClient.post(`/api/training/courses/${courseId}/enroll`),
    getProgress: (courseId: string) => 
      apiClient.get(`/api/training/courses/${courseId}/progress`),
    getModuleContent: (moduleId: string) => 
      apiClient.get(`/api/training/modules/${moduleId}/content`),
    completeModule: (moduleId: string, data: any) => 
      apiClient.post(`/api/training/modules/${moduleId}/complete`, data),
    submitQuiz: (moduleId: string, data: any) => 
      apiClient.post(`/api/training/modules/${moduleId}/quiz/submit`, data),
    getCertificates: () => 
      apiClient.get('/api/training/certificates'),
  },

  // Schemes
  schemes: {
    getSchemes: (params?: any) => 
      apiClient.get('/api/schemes/schemes', { params }),
    getSchemeDetails: (schemeId: string) => 
      apiClient.get(`/api/schemes/schemes/${schemeId}`),
    checkEligibility: (schemeId: string) => 
      apiClient.post(`/api/schemes/schemes/${schemeId}/eligibility`),
    bookmarkScheme: (schemeId: string) => 
      apiClient.post(`/api/schemes/schemes/${schemeId}/bookmark`),
    getBookmarks: () => 
      apiClient.get('/api/schemes/bookmarks'),
    applyToScheme: (schemeId: string, data: any) => 
      apiClient.post(`/api/schemes/schemes/${schemeId}/apply`, data),
    getApplications: () => 
      apiClient.get('/api/schemes/applications'),
  },
};

export default apiClient;
