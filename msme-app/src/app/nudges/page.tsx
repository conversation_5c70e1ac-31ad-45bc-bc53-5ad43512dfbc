'use client';

import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  TrendingUp,
  Filter,
  Search,
  MoreVertical,
  ExternalLink,
  Eye,
  EyeOff
} from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Mock data for nudges
const mockNudges = [
  {
    nudge_id: '1',
    title: 'GST Filing Due Soon',
    message: 'Your GST filing for December 2023 is due in 3 days. File now to avoid penalties and maintain your compliance score.',
    category: 'gst_compliance',
    status: 'sent',
    action_text: 'File GST Return',
    action_url: '/gst-filing',
    created_at: '2024-01-20T10:00:00Z',
    read_at: null,
    target_component: 'gst_compliance',
    engagement_score: null,
    priority: 'high'
  },
  {
    nudge_id: '2',
    title: 'Boost Your UPI Transactions',
    message: 'Increase your UPI transaction frequency to improve your digital payment score. Aim for at least 50 transactions this month.',
    category: 'upi_usage',
    status: 'sent',
    action_text: 'View UPI Guide',
    action_url: '/upi-guide',
    created_at: '2024-01-19T15:30:00Z',
    read_at: '2024-01-19T16:00:00Z',
    target_component: 'upi_diversity',
    engagement_score: 75,
    priority: 'medium'
  },
  {
    nudge_id: '3',
    title: 'Improve Online Presence',
    message: 'Your business could benefit from better online reviews. Encourage satisfied customers to leave reviews on Google and other platforms.',
    category: 'business_growth',
    status: 'sent',
    action_text: 'Learn More',
    action_url: '/online-presence',
    created_at: '2024-01-18T09:15:00Z',
    read_at: null,
    target_component: 'digital_presence',
    engagement_score: null,
    priority: 'low'
  },
  {
    nudge_id: '4',
    title: 'Cash Flow Optimization',
    message: 'Your recent cash flow patterns show room for improvement. Consider implementing better inventory management practices.',
    category: 'financial_health',
    status: 'sent',
    action_text: 'View Tips',
    action_url: '/cash-flow-tips',
    created_at: '2024-01-17T14:20:00Z',
    read_at: '2024-01-17T14:45:00Z',
    target_component: 'financial_health',
    engagement_score: 60,
    priority: 'medium'
  },
  {
    nudge_id: '5',
    title: 'Training Opportunity',
    message: 'Complete the "Digital Marketing for MSMEs" course to improve your business visibility and customer reach.',
    category: 'business_growth',
    status: 'sent',
    action_text: 'Start Course',
    action_url: '/training/digital-marketing',
    created_at: '2024-01-16T11:00:00Z',
    read_at: null,
    target_component: 'business_stability',
    engagement_score: null,
    priority: 'low'
  }
];

const categoryColors = {
  gst_compliance: 'bg-orange-100 text-orange-800',
  upi_usage: 'bg-blue-100 text-blue-800',
  score_improvement: 'bg-green-100 text-green-800',
  financial_health: 'bg-yellow-100 text-yellow-800',
  business_growth: 'bg-purple-100 text-purple-800'
};

const priorityColors = {
  high: 'bg-red-100 text-red-800',
  medium: 'bg-yellow-100 text-yellow-800',
  low: 'bg-green-100 text-green-800'
};

const categoryIcons = {
  gst_compliance: AlertCircle,
  upi_usage: TrendingUp,
  score_improvement: CheckCircle,
  financial_health: Clock,
  business_growth: Bell
};

export default function NudgesPage() {
  const [nudges, setNudges] = useState(mockNudges);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  const filteredNudges = nudges.filter(nudge => {
    const matchesSearch = nudge.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         nudge.message.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || nudge.category === selectedCategory;
    const matchesStatus = selectedStatus === 'all' || 
                         (selectedStatus === 'unread' && !nudge.read_at) ||
                         (selectedStatus === 'read' && nudge.read_at);
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const unreadCount = nudges.filter(n => !n.read_at).length;
  const totalEngagement = nudges.filter(n => n.engagement_score).reduce((acc, n) => acc + (n.engagement_score || 0), 0);
  const avgEngagement = totalEngagement / nudges.filter(n => n.engagement_score).length || 0;

  const handleMarkAsRead = async (nudgeId: string) => {
    setNudges(prev => prev.map(nudge => 
      nudge.nudge_id === nudgeId 
        ? { ...nudge, read_at: new Date().toISOString(), engagement_score: Math.floor(Math.random() * 40) + 60 }
        : nudge
    ));
  };

  const handleTakeAction = (nudge: any) => {
    if (!nudge.read_at) {
      handleMarkAsRead(nudge.nudge_id);
    }
    // In real app, this would navigate to the action URL
    console.log(`Taking action for nudge: ${nudge.title}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Nudges & Reminders</h1>
            <p className="text-gray-600">Stay on top of actions that improve your credit score</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">
              {unreadCount} unread
            </Badge>
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filters
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Nudges</CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{nudges.length}</div>
              <p className="text-xs text-muted-foreground">
                {unreadCount} pending actions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{avgEngagement.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                Average action completion
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Week</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">
                New nudges received
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search nudges..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Categories</option>
              <option value="gst_compliance">GST Compliance</option>
              <option value="upi_usage">UPI Usage</option>
              <option value="financial_health">Financial Health</option>
              <option value="business_growth">Business Growth</option>
            </select>
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Status</option>
              <option value="unread">Unread</option>
              <option value="read">Read</option>
            </select>
          </div>
        </div>

        {/* Nudges List */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">All Nudges ({filteredNudges.length})</TabsTrigger>
            <TabsTrigger value="unread">Unread ({filteredNudges.filter(n => !n.read_at).length})</TabsTrigger>
            <TabsTrigger value="high">High Priority ({filteredNudges.filter(n => n.priority === 'high').length})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            {filteredNudges.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Bell className="h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No nudges found</h3>
                  <p className="text-gray-500 text-center">
                    {searchTerm || selectedCategory !== 'all' || selectedStatus !== 'all'
                      ? 'Try adjusting your search or filters'
                      : 'You\'re all caught up! New nudges will appear here.'}
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredNudges.map((nudge) => {
                  const IconComponent = categoryIcons[nudge.category as keyof typeof categoryIcons];
                  return (
                    <Card key={nudge.nudge_id} className={`transition-all hover:shadow-md ${!nudge.read_at ? 'border-l-4 border-l-primary' : ''}`}>
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start space-x-4 flex-1">
                            <div className="flex-shrink-0">
                              <IconComponent className={`h-5 w-5 ${nudge.priority === 'high' ? 'text-red-500' : nudge.priority === 'medium' ? 'text-yellow-500' : 'text-green-500'}`} />
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center space-x-2 mb-2">
                                <h3 className="text-lg font-medium text-gray-900">{nudge.title}</h3>
                                {!nudge.read_at && (
                                  <div className="h-2 w-2 bg-primary rounded-full"></div>
                                )}
                              </div>
                              <p className="text-gray-600 mb-3">{nudge.message}</p>
                              <div className="flex items-center space-x-3 mb-3">
                                <Badge className={categoryColors[nudge.category as keyof typeof categoryColors]}>
                                  {nudge.category.replace('_', ' ')}
                                </Badge>
                                <Badge variant="outline" className={priorityColors[nudge.priority as keyof typeof priorityColors]}>
                                  {nudge.priority} priority
                                </Badge>
                                <span className="text-sm text-gray-500">
                                  {formatDate(nudge.created_at)}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <Button 
                                  size="sm" 
                                  onClick={() => handleTakeAction(nudge)}
                                  className="flex items-center"
                                >
                                  {nudge.action_text}
                                  <ExternalLink className="ml-2 h-3 w-3" />
                                </Button>
                                {!nudge.read_at && (
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => handleMarkAsRead(nudge.nudge_id)}
                                  >
                                    <Eye className="mr-2 h-3 w-3" />
                                    Mark as Read
                                  </Button>
                                )}
                                {nudge.engagement_score && (
                                  <Badge variant="secondary">
                                    {nudge.engagement_score}% engagement
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleMarkAsRead(nudge.nudge_id)}>
                                {nudge.read_at ? <EyeOff className="mr-2 h-4 w-4" /> : <Eye className="mr-2 h-4 w-4" />}
                                {nudge.read_at ? 'Mark as Unread' : 'Mark as Read'}
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Bell className="mr-2 h-4 w-4" />
                                Snooze
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>

          <TabsContent value="unread">
            <div className="space-y-4">
              {filteredNudges.filter(n => !n.read_at).map((nudge) => {
                const IconComponent = categoryIcons[nudge.category as keyof typeof categoryIcons];
                return (
                  <Card key={nudge.nudge_id} className="border-l-4 border-l-primary">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <IconComponent className="h-5 w-5 text-primary flex-shrink-0 mt-1" />
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900 mb-2">{nudge.title}</h3>
                          <p className="text-gray-600 mb-3">{nudge.message}</p>
                          <div className="flex items-center space-x-2">
                            <Button size="sm" onClick={() => handleTakeAction(nudge)}>
                              {nudge.action_text}
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleMarkAsRead(nudge.nudge_id)}
                            >
                              Mark as Read
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="high">
            <Alert className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                High priority nudges require immediate attention to maintain your credit score.
              </AlertDescription>
            </Alert>
            <div className="space-y-4">
              {filteredNudges.filter(n => n.priority === 'high').map((nudge) => {
                const IconComponent = categoryIcons[nudge.category as keyof typeof categoryIcons];
                return (
                  <Card key={nudge.nudge_id} className="border-l-4 border-l-red-500">
                    <CardContent className="p-6">
                      <div className="flex items-start space-x-4">
                        <IconComponent className="h-5 w-5 text-red-500 flex-shrink-0 mt-1" />
                        <div className="flex-1">
                          <h3 className="text-lg font-medium text-gray-900 mb-2">{nudge.title}</h3>
                          <p className="text-gray-600 mb-3">{nudge.message}</p>
                          <Button size="sm" onClick={() => handleTakeAction(nudge)} className="bg-red-600 hover:bg-red-700">
                            {nudge.action_text}
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
