'use client';

import React from 'react';
import Link from 'next/link';
import { 
  BarChart3, 
  Bell, 
  BookOpen, 
  FileText, 
  TrendingUp, 
  TrendingDown,
  ArrowRight,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';

// Mock data - in real app, this would come from API
const dashboardData = {
  ccrScore: {
    current: 75.5,
    previous: 72.3,
    trend: 'up',
    breakdown: {
      gst_compliance: 80,
      upi_diversity: 70,
      digital_presence: 75,
      financial_health: 78,
      business_stability: 74
    }
  },
  recentNudges: [
    {
      id: '1',
      title: 'GST Filing Reminder',
      message: 'Your GST filing is due in 3 days',
      category: 'gst_compliance',
      created_at: '2024-01-20T10:00:00Z',
      read_at: null,
      action_url: '/gst-filing'
    },
    {
      id: '2',
      title: 'Boost Digital Payments',
      message: 'Increase UPI transactions to improve your score',
      category: 'upi_usage',
      created_at: '2024-01-19T15:30:00Z',
      read_at: null,
      action_url: '/digital-payments'
    }
  ],
  trainingProgress: {
    enrolled: 3,
    completed: 1,
    inProgress: 2,
    nextCourse: {
      title: 'Digital Marketing for MSMEs',
      progress: 45,
      estimatedTime: '2 hours remaining'
    }
  },
  schemes: {
    eligible: 12,
    applied: 2,
    approved: 1,
    featured: {
      name: 'MUDRA Loan Scheme',
      type: 'loan',
      maxAmount: 1000000,
      interestRate: 8.5
    }
  }
};

export default function Dashboard() {
  const { ccrScore, recentNudges, trainingProgress, schemes } = dashboardData;

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Welcome back!</h1>
          <p className="text-gray-600">Here's what's happening with your business today.</p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* CCR Score Card */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">CCR Score</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{ccrScore.current}</div>
              <div className="flex items-center text-xs text-muted-foreground">
                {ccrScore.trend === 'up' ? (
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                )}
                {ccrScore.trend === 'up' ? '+' : '-'}
                {Math.abs(ccrScore.current - ccrScore.previous).toFixed(1)} from last month
              </div>
            </CardContent>
          </Card>

          {/* Unread Nudges */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Nudges</CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{recentNudges.filter(n => !n.read_at).length}</div>
              <p className="text-xs text-muted-foreground">
                Pending actions to improve score
              </p>
            </CardContent>
          </Card>

          {/* Training Progress */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Training</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{trainingProgress.completed}/{trainingProgress.enrolled}</div>
              <p className="text-xs text-muted-foreground">
                Courses completed
              </p>
            </CardContent>
          </Card>

          {/* Eligible Schemes */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Schemes</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{schemes.eligible}</div>
              <p className="text-xs text-muted-foreground">
                Eligible government schemes
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* CCR Score Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle>CCR Score Breakdown</CardTitle>
              <CardDescription>
                Your current score across all components
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(ccrScore.breakdown).map(([key, value]) => (
                <div key={key} className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="capitalize">{key.replace('_', ' ')}</span>
                    <span className="font-medium">{value}/100</span>
                  </div>
                  <Progress value={value} className="h-2" />
                </div>
              ))}
              <div className="pt-4">
                <Button asChild className="w-full">
                  <Link href="/ccr-score">
                    View Detailed Analysis
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Recent Nudges */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Nudges</CardTitle>
              <CardDescription>
                Actions to improve your credit score
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentNudges.slice(0, 3).map((nudge) => (
                <div key={nudge.id} className="flex items-start space-x-3 p-3 rounded-lg border">
                  <div className="flex-shrink-0">
                    {nudge.category === 'gst_compliance' ? (
                      <AlertCircle className="h-5 w-5 text-orange-500" />
                    ) : (
                      <CheckCircle className="h-5 w-5 text-blue-500" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900">{nudge.title}</p>
                    <p className="text-sm text-gray-500">{nudge.message}</p>
                    <div className="mt-2">
                      <Badge variant="secondary" className="text-xs">
                        {nudge.category.replace('_', ' ')}
                      </Badge>
                    </div>
                  </div>
                  {!nudge.read_at && (
                    <div className="flex-shrink-0">
                      <div className="h-2 w-2 bg-primary rounded-full"></div>
                    </div>
                  )}
                </div>
              ))}
              <div className="pt-2">
                <Button variant="outline" asChild className="w-full">
                  <Link href="/nudges">
                    View All Nudges
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Training Progress */}
          <Card>
            <CardHeader>
              <CardTitle>Training Progress</CardTitle>
              <CardDescription>
                Continue your learning journey
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Overall Progress</span>
                <span className="text-sm font-medium">
                  {trainingProgress.completed}/{trainingProgress.enrolled} completed
                </span>
              </div>
              <Progress 
                value={(trainingProgress.completed / trainingProgress.enrolled) * 100} 
                className="h-2" 
              />
              
              {trainingProgress.nextCourse && (
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium">{trainingProgress.nextCourse.title}</h4>
                    <Clock className="h-4 w-4 text-gray-400" />
                  </div>
                  <Progress value={trainingProgress.nextCourse.progress} className="h-2 mb-2" />
                  <p className="text-xs text-gray-500">{trainingProgress.nextCourse.estimatedTime}</p>
                </div>
              )}
              
              <Button asChild className="w-full">
                <Link href="/training">
                  Continue Learning
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </CardContent>
          </Card>

          {/* Featured Scheme */}
          <Card>
            <CardHeader>
              <CardTitle>Featured Scheme</CardTitle>
              <CardDescription>
                Recommended for your business
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">{schemes.featured.name}</h3>
                <Badge variant="outline" className="mt-1">
                  {schemes.featured.type}
                </Badge>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Max Amount:</span>
                  <span className="font-medium">₹{schemes.featured.maxAmount.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Interest Rate:</span>
                  <span className="font-medium">{schemes.featured.interestRate}% p.a.</span>
                </div>
              </div>
              
              <div className="space-y-2">
                <Button asChild className="w-full">
                  <Link href="/schemes">
                    Explore All Schemes
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <p className="text-xs text-center text-gray-500">
                  {schemes.eligible} schemes available for your business
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
