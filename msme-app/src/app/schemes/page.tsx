'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { 
  FileText, 
  Search, 
  Filter, 
  Bookmark, 
  ExternalLink,
  CheckCircle,
  Clock,
  AlertCircle,
  MapPin,
  Building,
  DollarSign,
  Calendar,
  Star,
  BookmarkCheck
} from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Mock data for government schemes
const mockSchemes = [
  {
    scheme_id: '1',
    name: 'MUDRA Loan Scheme',
    description: 'Micro Units Development and Refinance Agency provides loans to micro and small enterprises for business development and expansion.',
    short_description: 'Collateral-free loans up to ₹10 lakhs for micro enterprises',
    scheme_type: 'loan',
    status: 'active',
    implementing_agency: 'Small Industries Development Bank of India (SIDBI)',
    max_loan_amount: 1000000,
    interest_rate: 8.5,
    subsidy_percentage: null,
    processing_fee: 0,
    application_start_date: '2024-01-01',
    application_end_date: '2024-12-31',
    scheme_validity: '2024-12-31',
    processing_time_days: 30,
    required_documents: ['Aadhar Card', 'PAN Card', 'Business Registration', 'Bank Statements'],
    application_process: ['Online Application', 'Document Verification', 'Credit Assessment', 'Loan Disbursement'],
    official_website: 'https://mudra.org.in',
    contact_details: { phone: '1800-180-1111', email: '<EMAIL>' },
    application_link: 'https://mudra.org.in/apply',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
    tags: ['loan', 'micro-enterprise', 'collateral-free'],
    is_eligible: true,
    is_bookmarked: true,
    eligibility_score: 95
  },
  {
    scheme_id: '2',
    name: 'Stand-Up India Scheme',
    description: 'Facilitates bank loans between ₹10 lakh and ₹1 crore to at least one Scheduled Caste or Scheduled Tribe borrower and at least one woman borrower per bank branch.',
    short_description: 'Bank loans for SC/ST and women entrepreneurs',
    scheme_type: 'loan',
    status: 'active',
    implementing_agency: 'Department of Financial Services, Ministry of Finance',
    max_loan_amount: ********,
    interest_rate: 9.25,
    subsidy_percentage: null,
    processing_fee: 1,
    application_start_date: '2024-01-01',
    application_end_date: '2024-12-31',
    scheme_validity: '2024-12-31',
    processing_time_days: 45,
    required_documents: ['Identity Proof', 'Address Proof', 'Caste Certificate', 'Project Report'],
    application_process: ['Bank Application', 'Document Submission', 'Project Evaluation', 'Loan Sanction'],
    official_website: 'https://standupmitra.in',
    contact_details: { phone: '1800-180-1111', email: '<EMAIL>' },
    application_link: 'https://standupmitra.in/apply',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-10T00:00:00Z',
    tags: ['loan', 'women', 'sc-st', 'entrepreneurship'],
    is_eligible: false,
    is_bookmarked: false,
    eligibility_score: 45
  },
  {
    scheme_id: '3',
    name: 'Credit Guarantee Fund Trust for Micro and Small Enterprises (CGTMSE)',
    description: 'Provides credit guarantee support to banks and financial institutions for collateral-free credit to micro and small enterprises.',
    short_description: 'Credit guarantee for collateral-free loans up to ₹2 crores',
    scheme_type: 'subsidy',
    status: 'active',
    implementing_agency: 'Small Industries Development Bank of India (SIDBI)',
    max_loan_amount: ********,
    interest_rate: null,
    subsidy_percentage: 85,
    processing_fee: 0.75,
    application_start_date: '2024-01-01',
    application_end_date: '2024-12-31',
    scheme_validity: '2024-12-31',
    processing_time_days: 21,
    required_documents: ['Business Registration', 'Financial Statements', 'Project Report', 'Bank NOC'],
    application_process: ['Bank Application', 'CGTMSE Registration', 'Guarantee Approval', 'Loan Processing'],
    official_website: 'https://cgtmse.in',
    contact_details: { phone: '1800-180-2222', email: '<EMAIL>' },
    application_link: 'https://cgtmse.in/apply',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-12T00:00:00Z',
    tags: ['guarantee', 'collateral-free', 'msme'],
    is_eligible: true,
    is_bookmarked: false,
    eligibility_score: 88
  },
  {
    scheme_id: '4',
    name: 'Prime Minister Employment Generation Programme (PMEGP)',
    description: 'Credit linked subsidy programme for generating employment opportunities through establishment of micro enterprises in rural and urban areas.',
    short_description: 'Subsidy for new micro enterprises and employment generation',
    scheme_type: 'subsidy',
    status: 'active',
    implementing_agency: 'Khadi and Village Industries Commission (KVIC)',
    max_loan_amount: 2500000,
    interest_rate: null,
    subsidy_percentage: 35,
    processing_fee: 0,
    application_start_date: '2024-04-01',
    application_end_date: '2024-03-31',
    scheme_validity: '2025-03-31',
    processing_time_days: 60,
    required_documents: ['Educational Certificates', 'Experience Certificate', 'Project Report', 'Land Documents'],
    application_process: ['Online Application', 'DIC Verification', 'Bank Loan Processing', 'Subsidy Release'],
    official_website: 'https://kviconline.gov.in/pmegpeportal',
    contact_details: { phone: '1800-180-3333', email: '<EMAIL>' },
    application_link: 'https://kviconline.gov.in/pmegpeportal/jsp/pmegponline.jsp',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-08T00:00:00Z',
    tags: ['subsidy', 'employment', 'rural', 'urban'],
    is_eligible: true,
    is_bookmarked: true,
    eligibility_score: 92
  },
  {
    scheme_id: '5',
    name: 'Technology Upgradation Fund Scheme (TUFS)',
    description: 'Provides capital investment subsidy for technology upgradation in textile and jute industries.',
    short_description: 'Capital investment subsidy for textile industry modernization',
    scheme_type: 'subsidy',
    status: 'active',
    implementing_agency: 'Ministry of Textiles',
    max_loan_amount: ********,
    interest_rate: null,
    subsidy_percentage: 15,
    processing_fee: 0.5,
    application_start_date: '2024-01-01',
    application_end_date: '2024-12-31',
    scheme_validity: '2024-12-31',
    processing_time_days: 90,
    required_documents: ['Industry License', 'Environmental Clearance', 'Technology Details', 'Financial Projections'],
    application_process: ['Online Application', 'Technical Evaluation', 'Financial Appraisal', 'Subsidy Sanction'],
    official_website: 'https://texmin.nic.in',
    contact_details: { phone: '1800-180-4444', email: '<EMAIL>' },
    application_link: 'https://texmin.nic.in/tufs-apply',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-05T00:00:00Z',
    tags: ['subsidy', 'technology', 'textile', 'modernization'],
    is_eligible: false,
    is_bookmarked: false,
    eligibility_score: 25
  }
];

const mockApplications = [
  {
    application_id: '1',
    scheme_id: '1',
    scheme_name: 'MUDRA Loan Scheme',
    status: 'approved',
    applied_at: '2024-01-10T10:00:00Z',
    amount_applied: 500000,
    amount_sanctioned: 500000
  },
  {
    application_id: '2',
    scheme_id: '4',
    scheme_name: 'PMEGP',
    status: 'under_review',
    applied_at: '2024-01-15T14:30:00Z',
    amount_applied: 1000000,
    amount_sanctioned: null
  }
];

const schemeTypeColors = {
  loan: 'bg-blue-100 text-blue-800',
  subsidy: 'bg-green-100 text-green-800',
  grant: 'bg-purple-100 text-purple-800',
  tax_benefit: 'bg-orange-100 text-orange-800'
};

const statusColors = {
  approved: 'bg-green-100 text-green-800',
  under_review: 'bg-yellow-100 text-yellow-800',
  rejected: 'bg-red-100 text-red-800',
  draft: 'bg-gray-100 text-gray-800'
};

export default function SchemesPage() {
  const [schemes, setSchemes] = useState(mockSchemes);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedEligibility, setSelectedEligibility] = useState('all');

  const filteredSchemes = schemes.filter(scheme => {
    const matchesSearch = scheme.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         scheme.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === 'all' || scheme.scheme_type === selectedType;
    const matchesEligibility = selectedEligibility === 'all' || 
                              (selectedEligibility === 'eligible' && scheme.is_eligible) ||
                              (selectedEligibility === 'not_eligible' && !scheme.is_eligible);
    
    return matchesSearch && matchesType && matchesEligibility;
  });

  const bookmarkedSchemes = schemes.filter(s => s.is_bookmarked);
  const eligibleSchemes = schemes.filter(s => s.is_eligible);

  const handleBookmark = (schemeId: string) => {
    setSchemes(prev => prev.map(scheme => 
      scheme.scheme_id === schemeId 
        ? { ...scheme, is_bookmarked: !scheme.is_bookmarked }
        : scheme
    ));
  };

  const formatAmount = (amount: number) => {
    if (amount >= ********) {
      return `₹${(amount / ********).toFixed(1)} Cr`;
    } else if (amount >= 100000) {
      return `₹${(amount / 100000).toFixed(1)} L`;
    } else {
      return `₹${amount.toLocaleString()}`;
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Government Schemes</h1>
            <p className="text-gray-600">Discover and apply for government schemes for your business</p>
          </div>
          <Button asChild variant="outline">
            <Link href="/schemes/applications">
              <FileText className="mr-2 h-4 w-4" />
              My Applications
            </Link>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Schemes</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{schemes.length}</div>
              <p className="text-xs text-muted-foreground">
                Available for MSMEs
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Eligible</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{eligibleSchemes.length}</div>
              <p className="text-xs text-muted-foreground">
                Based on your profile
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Bookmarked</CardTitle>
              <Bookmark className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{bookmarkedSchemes.length}</div>
              <p className="text-xs text-muted-foreground">
                Saved for later
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Applications</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockApplications.length}</div>
              <p className="text-xs text-muted-foreground">
                Submitted applications
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search schemes..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Types</option>
              <option value="loan">Loans</option>
              <option value="subsidy">Subsidies</option>
              <option value="grant">Grants</option>
              <option value="tax_benefit">Tax Benefits</option>
            </select>
            <select
              value={selectedEligibility}
              onChange={(e) => setSelectedEligibility(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Schemes</option>
              <option value="eligible">Eligible Only</option>
              <option value="not_eligible">Not Eligible</option>
            </select>
          </div>
        </div>

        {/* Schemes Tabs */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">All Schemes ({filteredSchemes.length})</TabsTrigger>
            <TabsTrigger value="eligible">Eligible ({eligibleSchemes.length})</TabsTrigger>
            <TabsTrigger value="bookmarked">Bookmarked ({bookmarkedSchemes.length})</TabsTrigger>
            <TabsTrigger value="applications">My Applications ({mockApplications.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredSchemes.map((scheme) => (
                <Card key={scheme.scheme_id} className={`hover:shadow-lg transition-shadow ${scheme.is_eligible ? 'border-l-4 border-l-green-500' : ''}`}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg mb-2 flex items-center">
                          {scheme.name}
                          {scheme.is_eligible && (
                            <CheckCircle className="ml-2 h-5 w-5 text-green-500" />
                          )}
                        </CardTitle>
                        <CardDescription className="text-sm">
                          {scheme.short_description}
                        </CardDescription>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleBookmark(scheme.scheme_id)}
                      >
                        {scheme.is_bookmarked ? (
                          <BookmarkCheck className="h-4 w-4 text-primary" />
                        ) : (
                          <Bookmark className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                    <div className="flex items-center space-x-2 mt-3">
                      <Badge className={schemeTypeColors[scheme.scheme_type as keyof typeof schemeTypeColors]}>
                        {scheme.scheme_type}
                      </Badge>
                      {scheme.is_eligible && (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          {scheme.eligibility_score}% match
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center">
                          <DollarSign className="mr-2 h-4 w-4 text-gray-400" />
                          <span>Max: {formatAmount(scheme.max_loan_amount)}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4 text-gray-400" />
                          <span>{scheme.processing_time_days} days</span>
                        </div>
                        {scheme.interest_rate && (
                          <div className="flex items-center">
                            <span className="mr-2">%</span>
                            <span>{scheme.interest_rate}% interest</span>
                          </div>
                        )}
                        {scheme.subsidy_percentage && (
                          <div className="flex items-center">
                            <Star className="mr-2 h-4 w-4 text-yellow-500" />
                            <span>{scheme.subsidy_percentage}% subsidy</span>
                          </div>
                        )}
                      </div>

                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Agency:</span> {scheme.implementing_agency}
                      </div>

                      <div className="flex space-x-2">
                        <Button 
                          className="flex-1"
                          disabled={!scheme.is_eligible}
                          asChild={scheme.is_eligible}
                        >
                          {scheme.is_eligible ? (
                            <Link href={`/schemes/${scheme.scheme_id}/apply`}>
                              Apply Now
                            </Link>
                          ) : (
                            <span>Not Eligible</span>
                          )}
                        </Button>
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/schemes/${scheme.scheme_id}`}>
                            <ExternalLink className="mr-2 h-4 w-4" />
                            Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="eligible">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {eligibleSchemes.map((scheme) => (
                <Card key={scheme.scheme_id} className="border-l-4 border-l-green-500">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      {scheme.name}
                      <CheckCircle className="ml-2 h-5 w-5 text-green-500" />
                    </CardTitle>
                    <CardDescription>{scheme.short_description}</CardDescription>
                    <Badge variant="outline" className="text-green-600 border-green-600 w-fit">
                      {scheme.eligibility_score}% match
                    </Badge>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-sm">
                        <span className="font-medium">Max Amount:</span> {formatAmount(scheme.max_loan_amount)}
                      </div>
                      <Button className="w-full" asChild>
                        <Link href={`/schemes/${scheme.scheme_id}/apply`}>
                          Apply Now
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="bookmarked">
            <div className="space-y-4">
              {bookmarkedSchemes.map((scheme) => (
                <Card key={scheme.scheme_id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-medium mb-2">{scheme.name}</h3>
                        <p className="text-gray-600 mb-3">{scheme.short_description}</p>
                        <div className="flex items-center space-x-4">
                          <Badge className={schemeTypeColors[scheme.scheme_type as keyof typeof schemeTypeColors]}>
                            {scheme.scheme_type}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            Max: {formatAmount(scheme.max_loan_amount)}
                          </span>
                          {scheme.is_eligible && (
                            <Badge variant="outline" className="text-green-600">
                              Eligible
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="ml-6 space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleBookmark(scheme.scheme_id)}
                        >
                          <BookmarkCheck className="h-4 w-4" />
                        </Button>
                        <Button asChild disabled={!scheme.is_eligible}>
                          {scheme.is_eligible ? (
                            <Link href={`/schemes/${scheme.scheme_id}/apply`}>
                              Apply
                            </Link>
                          ) : (
                            <span>Not Eligible</span>
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="applications">
            <div className="space-y-4">
              {mockApplications.map((application) => (
                <Card key={application.application_id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-medium mb-2">{application.scheme_name}</h3>
                        <div className="flex items-center space-x-4 mb-3">
                          <Badge className={statusColors[application.status as keyof typeof statusColors]}>
                            {application.status.replace('_', ' ')}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            Applied: {new Date(application.applied_at).toLocaleDateString()}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600">
                          <span className="font-medium">Amount Applied:</span> {formatAmount(application.amount_applied)}
                          {application.amount_sanctioned && (
                            <span className="ml-4">
                              <span className="font-medium">Sanctioned:</span> {formatAmount(application.amount_sanctioned)}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="ml-6">
                        <Button variant="outline" asChild>
                          <Link href={`/schemes/applications/${application.application_id}`}>
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
