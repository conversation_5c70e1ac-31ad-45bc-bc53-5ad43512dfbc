'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem('msme_auth_token');
    if (token) {
      router.push('/dashboard');
    } else {
      router.push('/auth/login');
    }
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="h-12 w-12 rounded-lg bg-primary mx-auto mb-4 flex items-center justify-center">
          <span className="text-white font-bold text-lg">CC</span>
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Credit Chakra</h1>
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
}
