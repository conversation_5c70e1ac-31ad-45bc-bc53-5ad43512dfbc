'use client';

import React, { useState } from 'react';
import { 
  BarChart3, 
  Download, 
  QrCode, 
  TrendingUp, 
  TrendingDown,
  Info,
  Calendar,
  Target
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Mock data
const ccrData = {
  current: {
    score: 75.5,
    risk_band: 'yellow' as const,
    risk_label: 'Medium',
    last_updated: '2024-01-20T10:00:00Z',
    breakdown: {
      gst_compliance: 80,
      upi_diversity: 70,
      digital_presence: 75,
      financial_health: 78,
      business_stability: 74
    }
  },
  trends: [
    { date: '2023-07', score: 68.2, gst_compliance: 75, upi_diversity: 65, digital_presence: 70, financial_health: 72, business_stability: 69 },
    { date: '2023-08', score: 70.1, gst_compliance: 77, upi_diversity: 67, digital_presence: 72, financial_health: 74, business_stability: 71 },
    { date: '2023-09', score: 71.8, gst_compliance: 78, upi_diversity: 68, digital_presence: 73, financial_health: 75, business_stability: 72 },
    { date: '2023-10', score: 72.3, gst_compliance: 79, upi_diversity: 69, digital_presence: 74, financial_health: 76, business_stability: 73 },
    { date: '2023-11', score: 74.1, gst_compliance: 80, upi_diversity: 70, digital_presence: 75, financial_health: 77, business_stability: 74 },
    { date: '2023-12', score: 75.5, gst_compliance: 80, upi_diversity: 70, digital_presence: 75, financial_health: 78, business_stability: 74 },
  ],
  recommendations: [
    {
      component: 'upi_diversity',
      current: 70,
      target: 80,
      impact: '+3.2 points',
      action: 'Increase UPI transaction frequency and diversify payment methods'
    },
    {
      component: 'digital_presence',
      current: 75,
      target: 85,
      impact: '+2.8 points',
      action: 'Improve online reviews and social media presence'
    },
    {
      component: 'business_stability',
      current: 74,
      target: 80,
      impact: '+2.1 points',
      action: 'Maintain consistent business operations and growth'
    }
  ]
};

const componentColors = {
  gst_compliance: '#10b981',
  upi_diversity: '#3b82f6',
  digital_presence: '#8b5cf6',
  financial_health: '#f59e0b',
  business_stability: '#ef4444'
};

const componentLabels = {
  gst_compliance: 'GST Compliance',
  upi_diversity: 'UPI Diversity',
  digital_presence: 'Digital Presence',
  financial_health: 'Financial Health',
  business_stability: 'Business Stability'
};

export default function CCRScorePage() {
  const [isExporting, setIsExporting] = useState(false);
  const { current, trends, recommendations } = ccrData;

  const handleExport = async (format: 'pdf' | 'qr') => {
    setIsExporting(true);
    try {
      // Mock export functionality
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In real app, this would call the API and download the file
      console.log(`Exporting CCR score as ${format}`);
      
      // Simulate file download
      const filename = `ccr-score-${Date.now()}.${format === 'pdf' ? 'pdf' : 'png'}`;
      alert(`${format.toUpperCase()} export completed: ${filename}`);
    } catch (error) {
      console.error('Export failed:', error);
    } finally {
      setIsExporting(false);
    }
  };

  const pieData = Object.entries(current.breakdown).map(([key, value]) => ({
    name: componentLabels[key as keyof typeof componentLabels],
    value,
    color: componentColors[key as keyof typeof componentColors]
  }));

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">CCR Score Dashboard</h1>
            <p className="text-gray-600">Monitor and improve your Credit Chakra Rating</p>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={() => handleExport('qr')}
              disabled={isExporting}
            >
              <QrCode className="mr-2 h-4 w-4" />
              QR Code
            </Button>
            <Button 
              onClick={() => handleExport('pdf')}
              disabled={isExporting}
            >
              <Download className="mr-2 h-4 w-4" />
              {isExporting ? 'Exporting...' : 'Export PDF'}
            </Button>
          </div>
        </div>

        {/* Current Score Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Current CCR Score
                <Badge 
                  variant={current.risk_band === 'green' ? 'default' : current.risk_band === 'yellow' ? 'secondary' : 'destructive'}
                >
                  {current.risk_label} Risk
                </Badge>
              </CardTitle>
              <CardDescription>
                Your overall Credit Chakra Rating out of 100
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <div className="text-4xl font-bold text-primary">{current.score}</div>
                <div className="flex-1">
                  <Progress value={current.score} className="h-3" />
                  <div className="flex justify-between text-xs text-gray-500 mt-1">
                    <span>0</span>
                    <span>50</span>
                    <span>100</span>
                  </div>
                </div>
              </div>
              <div className="mt-4 flex items-center text-sm text-gray-600">
                <Calendar className="mr-2 h-4 w-4" />
                Last updated: {new Date(current.last_updated).toLocaleDateString()}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Score Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Analysis */}
        <Tabs defaultValue="breakdown" className="space-y-4">
          <TabsList>
            <TabsTrigger value="breakdown">Score Breakdown</TabsTrigger>
            <TabsTrigger value="trends">Trends</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          <TabsContent value="breakdown" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(current.breakdown).map(([key, value]) => (
                <Card key={key}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium flex items-center">
                      <div 
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: componentColors[key as keyof typeof componentColors] }}
                      />
                      {componentLabels[key as keyof typeof componentLabels]}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold mb-2">{value}/100</div>
                    <Progress value={value} className="h-2" />
                    <div className="mt-2 text-xs text-gray-500">
                      {value >= 80 ? 'Excellent' : value >= 60 ? 'Good' : value >= 40 ? 'Fair' : 'Needs Improvement'}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="trends" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Score Trends (Last 6 Months)</CardTitle>
                <CardDescription>
                  Track your CCR score improvement over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={trends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip />
                      <Line 
                        type="monotone" 
                        dataKey="score" 
                        stroke="#10b981" 
                        strokeWidth={3}
                        dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Component Trends</CardTitle>
                <CardDescription>
                  Individual component performance over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={trends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip />
                      {Object.entries(componentColors).map(([key, color]) => (
                        <Line 
                          key={key}
                          type="monotone" 
                          dataKey={key} 
                          stroke={color}
                          strokeWidth={2}
                          dot={{ fill: color, strokeWidth: 1, r: 3 }}
                        />
                      ))}
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="recommendations" className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                These recommendations are based on your current score and can help improve your CCR rating.
              </AlertDescription>
            </Alert>

            <div className="space-y-4">
              {recommendations.map((rec, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span className="flex items-center">
                        <div 
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: componentColors[rec.component as keyof typeof componentColors] }}
                        />
                        {componentLabels[rec.component as keyof typeof componentLabels]}
                      </span>
                      <Badge variant="outline" className="text-green-600">
                        {rec.impact}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span>Current Score</span>
                        <span className="font-medium">{rec.current}/100</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>Target Score</span>
                        <span className="font-medium text-green-600">{rec.target}/100</span>
                      </div>
                      <Progress value={rec.current} className="h-2" />
                      <p className="text-sm text-gray-600">{rec.action}</p>
                      <Button size="sm" className="w-full">
                        <Target className="mr-2 h-4 w-4" />
                        Take Action
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
