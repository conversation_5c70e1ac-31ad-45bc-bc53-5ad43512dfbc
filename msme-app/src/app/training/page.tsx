'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { 
  <PERSON><PERSON><PERSON>, 
  Clock, 
  Users, 
  Award, 
  Play,
  CheckCircle,
  Star,
  Filter,
  Search,
  TrendingUp,
  Target,
  Calendar
} from 'lucide-react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Mock data for training courses
const mockCourses = [
  {
    course_id: '1',
    title: 'Digital Marketing for MSMEs',
    description: 'Learn how to leverage digital platforms to grow your business and reach more customers online.',
    category: 'Marketing',
    difficulty_level: 'beginner',
    estimated_duration_minutes: 180,
    modules: ['intro', 'social-media', 'seo', 'analytics'],
    prerequisites: [],
    target_business_types: ['retail', 'services'],
    is_active: true,
    created_at: '2024-01-15T10:00:00Z',
    thumbnail_url: '/course-thumbnails/digital-marketing.jpg',
    instructor_name: 'Dr. Priya Sharma',
    rating: 4.8,
    enrolled_count: 1250,
    completion_rate: 85,
    progress: 45
  },
  {
    course_id: '2',
    title: 'GST Compliance Made Easy',
    description: 'Master GST filing, compliance requirements, and best practices to maintain your tax obligations.',
    category: 'Finance & Compliance',
    difficulty_level: 'intermediate',
    estimated_duration_minutes: 240,
    modules: ['gst-basics', 'filing-process', 'compliance', 'penalties'],
    prerequisites: [],
    target_business_types: ['retail', 'b2b', 'services', 'manufacturing'],
    is_active: true,
    created_at: '2024-01-10T10:00:00Z',
    thumbnail_url: '/course-thumbnails/gst-compliance.jpg',
    instructor_name: 'CA Rajesh Kumar',
    rating: 4.9,
    enrolled_count: 2100,
    completion_rate: 92,
    progress: 0
  },
  {
    course_id: '3',
    title: 'Financial Management for Small Business',
    description: 'Learn essential financial management skills including cash flow, budgeting, and financial planning.',
    category: 'Finance & Compliance',
    difficulty_level: 'intermediate',
    estimated_duration_minutes: 300,
    modules: ['cash-flow', 'budgeting', 'financial-planning', 'investment'],
    prerequisites: [],
    target_business_types: ['retail', 'b2b', 'services', 'manufacturing'],
    is_active: true,
    created_at: '2024-01-05T10:00:00Z',
    thumbnail_url: '/course-thumbnails/financial-management.jpg',
    instructor_name: 'Prof. Anita Desai',
    rating: 4.7,
    enrolled_count: 890,
    completion_rate: 78,
    progress: 100
  },
  {
    course_id: '4',
    title: 'Digital Payments & UPI for Business',
    description: 'Understand digital payment systems, UPI integration, and how to maximize transaction efficiency.',
    category: 'Technology',
    difficulty_level: 'beginner',
    estimated_duration_minutes: 120,
    modules: ['upi-basics', 'payment-gateway', 'security', 'analytics'],
    prerequisites: [],
    target_business_types: ['retail', 'services'],
    is_active: true,
    created_at: '2024-01-01T10:00:00Z',
    thumbnail_url: '/course-thumbnails/digital-payments.jpg',
    instructor_name: 'Mr. Vikram Singh',
    rating: 4.6,
    enrolled_count: 1500,
    completion_rate: 88,
    progress: 0
  },
  {
    course_id: '5',
    title: 'Customer Service Excellence',
    description: 'Build exceptional customer service skills to improve customer satisfaction and retention.',
    category: 'Business Skills',
    difficulty_level: 'beginner',
    estimated_duration_minutes: 150,
    modules: ['customer-psychology', 'communication', 'problem-solving', 'retention'],
    prerequisites: [],
    target_business_types: ['retail', 'services'],
    is_active: true,
    created_at: '2023-12-20T10:00:00Z',
    thumbnail_url: '/course-thumbnails/customer-service.jpg',
    instructor_name: 'Ms. Kavita Patel',
    rating: 4.5,
    enrolled_count: 750,
    completion_rate: 82,
    progress: 0
  }
];

const mockProgress = {
  enrolled: 3,
  completed: 1,
  in_progress: 2,
  total_hours: 12.5,
  certificates: 1,
  current_streak: 5
};

const difficultyColors = {
  beginner: 'bg-green-100 text-green-800',
  intermediate: 'bg-yellow-100 text-yellow-800',
  advanced: 'bg-red-100 text-red-800'
};

const categoryColors = {
  'Marketing': 'bg-purple-100 text-purple-800',
  'Finance & Compliance': 'bg-blue-100 text-blue-800',
  'Technology': 'bg-green-100 text-green-800',
  'Business Skills': 'bg-orange-100 text-orange-800'
};

export default function TrainingPage() {
  const [courses, setCourses] = useState(mockCourses);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');

  const filteredCourses = courses.filter(course => {
    const matchesSearch = course.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         course.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || course.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || course.difficulty_level === selectedDifficulty;
    
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const enrolledCourses = courses.filter(c => c.progress > 0);
  const availableCourses = courses.filter(c => c.progress === 0);
  const completedCourses = courses.filter(c => c.progress === 100);

  const handleEnrollCourse = (courseId: string) => {
    setCourses(prev => prev.map(course => 
      course.course_id === courseId 
        ? { ...course, progress: 1, enrolled_count: course.enrolled_count + 1 }
        : course
    ));
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Training & Education</h1>
            <p className="text-gray-600">Enhance your business skills and knowledge</p>
          </div>
          <Button asChild>
            <Link href="/training/certificates">
              <Award className="mr-2 h-4 w-4" />
              My Certificates
            </Link>
          </Button>
        </div>

        {/* Progress Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Enrolled Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockProgress.enrolled}</div>
              <p className="text-xs text-muted-foreground">
                {mockProgress.in_progress} in progress
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockProgress.completed}</div>
              <p className="text-xs text-muted-foreground">
                {mockProgress.certificates} certificates earned
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Learning Hours</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockProgress.total_hours}h</div>
              <p className="text-xs text-muted-foreground">
                Total time invested
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Streak</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{mockProgress.current_streak}</div>
              <p className="text-xs text-muted-foreground">
                Days of learning
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search courses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Categories</option>
              <option value="Marketing">Marketing</option>
              <option value="Finance & Compliance">Finance & Compliance</option>
              <option value="Technology">Technology</option>
              <option value="Business Skills">Business Skills</option>
            </select>
            <select
              value={selectedDifficulty}
              onChange={(e) => setSelectedDifficulty(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            >
              <option value="all">All Levels</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>
          </div>
        </div>

        {/* Course Tabs */}
        <Tabs defaultValue="all" className="space-y-4">
          <TabsList>
            <TabsTrigger value="all">All Courses ({filteredCourses.length})</TabsTrigger>
            <TabsTrigger value="enrolled">My Courses ({enrolledCourses.length})</TabsTrigger>
            <TabsTrigger value="available">Available ({availableCourses.length})</TabsTrigger>
            <TabsTrigger value="completed">Completed ({completedCourses.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCourses.map((course) => (
                <Card key={course.course_id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg mb-2">{course.title}</CardTitle>
                        <CardDescription className="text-sm line-clamp-2">
                          {course.description}
                        </CardDescription>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 mt-3">
                      <Badge className={categoryColors[course.category as keyof typeof categoryColors]}>
                        {course.category}
                      </Badge>
                      <Badge variant="outline" className={difficultyColors[course.difficulty_level as keyof typeof difficultyColors]}>
                        {course.difficulty_level}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <div className="flex items-center">
                          <Clock className="mr-1 h-4 w-4" />
                          {formatDuration(course.estimated_duration_minutes)}
                        </div>
                        <div className="flex items-center">
                          <Users className="mr-1 h-4 w-4" />
                          {course.enrolled_count}
                        </div>
                        <div className="flex items-center">
                          <Star className="mr-1 h-4 w-4 text-yellow-500" />
                          {course.rating}
                        </div>
                      </div>

                      {course.progress > 0 && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{course.progress}%</span>
                          </div>
                          <Progress value={course.progress} className="h-2" />
                        </div>
                      )}

                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Instructor:</span> {course.instructor_name}
                      </div>

                      <div className="flex space-x-2">
                        {course.progress === 0 ? (
                          <Button 
                            className="flex-1"
                            onClick={() => handleEnrollCourse(course.course_id)}
                          >
                            <BookOpen className="mr-2 h-4 w-4" />
                            Enroll Now
                          </Button>
                        ) : course.progress === 100 ? (
                          <Button variant="outline" className="flex-1" asChild>
                            <Link href={`/training/courses/${course.course_id}/certificate`}>
                              <Award className="mr-2 h-4 w-4" />
                              View Certificate
                            </Link>
                          </Button>
                        ) : (
                          <Button className="flex-1" asChild>
                            <Link href={`/training/courses/${course.course_id}`}>
                              <Play className="mr-2 h-4 w-4" />
                              Continue Learning
                            </Link>
                          </Button>
                        )}
                        <Button variant="outline" size="sm" asChild>
                          <Link href={`/training/courses/${course.course_id}/preview`}>
                            Preview
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="enrolled">
            <div className="space-y-4">
              {enrolledCourses.map((course) => (
                <Card key={course.course_id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-medium mb-2">{course.title}</h3>
                        <p className="text-gray-600 mb-3">{course.description}</p>
                        <div className="flex items-center space-x-4 mb-3">
                          <Badge className={categoryColors[course.category as keyof typeof categoryColors]}>
                            {course.category}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            {formatDuration(course.estimated_duration_minutes)}
                          </span>
                          <span className="text-sm text-gray-500">
                            {course.modules.length} modules
                          </span>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{course.progress}%</span>
                          </div>
                          <Progress value={course.progress} className="h-2" />
                        </div>
                      </div>
                      <div className="ml-6">
                        <Button asChild>
                          <Link href={`/training/courses/${course.course_id}`}>
                            {course.progress === 100 ? 'Review' : 'Continue'}
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="available">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {availableCourses.map((course) => (
                <Card key={course.course_id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle className="text-lg">{course.title}</CardTitle>
                    <CardDescription>{course.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <Badge className={categoryColors[course.category as keyof typeof categoryColors]}>
                          {course.category}
                        </Badge>
                        <Badge variant="outline" className={difficultyColors[course.difficulty_level as keyof typeof difficultyColors]}>
                          {course.difficulty_level}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>{formatDuration(course.estimated_duration_minutes)}</span>
                        <span>{course.enrolled_count} enrolled</span>
                        <span>★ {course.rating}</span>
                      </div>
                      <Button 
                        className="w-full"
                        onClick={() => handleEnrollCourse(course.course_id)}
                      >
                        <BookOpen className="mr-2 h-4 w-4" />
                        Enroll Now
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="completed">
            <div className="space-y-4">
              {completedCourses.map((course) => (
                <Card key={course.course_id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h3 className="text-lg font-medium">{course.title}</h3>
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        </div>
                        <p className="text-gray-600 mb-3">{course.description}</p>
                        <div className="flex items-center space-x-4">
                          <Badge className={categoryColors[course.category as keyof typeof categoryColors]}>
                            {course.category}
                          </Badge>
                          <span className="text-sm text-gray-500">
                            Completed on {new Date().toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <div className="ml-6 space-x-2">
                        <Button variant="outline" asChild>
                          <Link href={`/training/courses/${course.course_id}/certificate`}>
                            <Award className="mr-2 h-4 w-4" />
                            Certificate
                          </Link>
                        </Button>
                        <Button variant="outline" asChild>
                          <Link href={`/training/courses/${course.course_id}`}>
                            Review
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
