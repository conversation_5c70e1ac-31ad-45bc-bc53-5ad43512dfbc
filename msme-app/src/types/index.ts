/**
 * Type definitions for MSME-facing application
 */

// Business Types
export type BusinessType = 'retail' | 'b2b' | 'services' | 'manufacturing';

// Risk Bands
export type RiskBand = 'green' | 'yellow' | 'red';

// MSME Profile
export interface MSMEProfile {
  msme_id: string;
  name: string;
  business_type: BusinessType;
  location: string;
  email?: string;
  phone?: string;
  gst_number?: string;
  created_at: string;
  is_verified: boolean;
  ccr_score?: number;
  risk_band?: RiskBand;
  tags: string[];
}

// CCR Score Breakdown
export interface CCRScoreBreakdown {
  gst_compliance: number;
  upi_diversity: number;
  digital_presence: number;
  financial_health: number;
  business_stability: number;
}

// CCR Score Response
export interface CCRScoreResponse {
  msme_id: string;
  msme_name: string;
  ccr_score: number;
  risk_band: RiskBand;
  risk_label: string;
  score_breakdown: CCRScoreBreakdown;
  last_updated: string;
  score_scale: string;
}

// CCR Trend Data
export interface CCRTrend {
  date: string;
  score: number;
  breakdown: CCRScoreBreakdown;
}

// Nudge Types
export type TriggerType = 
  | 'gst_filing_due'
  | 'low_upi_activity'
  | 'score_decline'
  | 'cash_flow_alert'
  | 'growth_opportunity';

export type NudgeCategory = 
  | 'gst_compliance'
  | 'upi_usage'
  | 'score_improvement'
  | 'financial_health'
  | 'business_growth';

export type Medium = 'in_app' | 'email' | 'sms' | 'whatsapp';

export interface BehavioralNudge {
  nudge_id: string;
  title: string;
  message: string;
  category: NudgeCategory;
  status: string;
  action_text?: string;
  action_url?: string;
  created_at: string;
  read_at?: string;
  target_component?: string;
  engagement_score?: number;
}

// Training Types
export type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced';
export type CourseStatus = 'not_started' | 'in_progress' | 'completed' | 'expired';

export interface TrainingCourse {
  course_id: string;
  title: string;
  description: string;
  category: string;
  difficulty_level: DifficultyLevel;
  estimated_duration_minutes: number;
  modules: string[];
  prerequisites: string[];
  target_business_types: BusinessType[];
  is_active: boolean;
  created_at: string;
  thumbnail_url?: string;
  instructor_name?: string;
}

export interface UserCourseProgress {
  progress_id: string;
  msme_id: string;
  course_id: string;
  status: CourseStatus;
  started_at?: string;
  completed_at?: string;
  last_accessed?: string;
  completed_modules: string[];
  current_module_id?: string;
  quiz_attempts: any[];
  best_score?: number;
  total_time_spent_minutes: number;
  certificate_issued: boolean;
}

export interface TrainingModule {
  module_id: string;
  course_id: string;
  title: string;
  description: string;
  content_type: string;
  content_url?: string;
  duration_minutes: number;
  order_index: number;
  has_quiz: boolean;
  passing_score?: number;
  is_mandatory: boolean;
}

// Scheme Types
export type SchemeType = 
  | 'loan'
  | 'subsidy'
  | 'grant'
  | 'tax_benefit'
  | 'training'
  | 'infrastructure'
  | 'technology'
  | 'export_promotion';

export type ApplicationStatus = 
  | 'not_applied'
  | 'draft'
  | 'submitted'
  | 'under_review'
  | 'approved'
  | 'rejected'
  | 'disbursed';

export interface GovernmentScheme {
  scheme_id: string;
  name: string;
  description: string;
  short_description: string;
  scheme_type: SchemeType;
  status: string;
  implementing_agency: string;
  max_loan_amount?: number;
  interest_rate?: number;
  subsidy_percentage?: number;
  processing_fee?: number;
  application_start_date?: string;
  application_end_date?: string;
  scheme_validity?: string;
  processing_time_days?: number;
  required_documents: string[];
  application_process: string[];
  official_website?: string;
  contact_details: Record<string, string>;
  application_link?: string;
  created_at: string;
  updated_at: string;
  tags: string[];
  is_eligible?: boolean;
  is_bookmarked?: boolean;
}

export interface SchemeApplication {
  application_id: string;
  msme_id: string;
  scheme_id: string;
  status: ApplicationStatus;
  applied_at: string;
  documents_submitted: string[];
  application_data: Record<string, any>;
  status_history: any[];
  expected_completion_date?: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
}

// Auth Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  business_name: string;
  business_type: BusinessType;
  location: string;
  email: string;
  phone: string;
  password: string;
  gst_number?: string;
  tags?: string[];
}

export interface AuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  msme_profile: MSMEProfile;
}

// Component Props Types
export interface DashboardCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export interface ScoreComponentProps {
  label: string;
  score: number;
  maxScore: number;
  color?: string;
}

export interface ChartDataPoint {
  date: string;
  score: number;
  [key: string]: any;
}
