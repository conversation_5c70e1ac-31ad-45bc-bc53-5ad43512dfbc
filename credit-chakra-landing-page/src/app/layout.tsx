import type { Metadata } from 'next'
import { Inter, Poppins } from 'next/font/google'
import './globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const poppins = Poppins({ 
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900'],
  variable: '--font-poppins',
  display: 'swap',
})

export const metadata: Metadata = {
  title: 'Credit Chakra - Smarter MSME Credit Intelligence',
  description: 'AI-powered scoring, monitoring, and early-warning systems helping lenders and policymakers accelerate MSME growth.',
  keywords: 'MSME, credit scoring, AI, fintech, lending, risk assessment, financial technology',
  authors: [{ name: 'Credit Chakra Team' }],
  robots: 'index, follow',
  openGraph: {
    title: 'Credit Chakra - Smarter MSME Credit Intelligence',
    description: 'AI-powered scoring, monitoring, and early-warning systems helping lenders and policymakers accelerate MSME growth.',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Credit Chakra - Smarter MSME Credit Intelligence',
    description: 'AI-powered scoring, monitoring, and early-warning systems helping lenders and policymakers accelerate MSME growth.',
  },
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body className={`${inter.className} antialiased`}>
        {children}
      </body>
    </html>
  )
}
