"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { FileText, BookOpen, TrendingUp, Users, Download, ExternalLink } from "lucide-react"

const resources = [
  {
    icon: FileText,
    title: "API Documentation",
    description: "Complete technical documentation for seamless integration with Credit Chakra's APIs.",
    type: "Documentation",
    link: "#",
    color: "bg-blue-500"
  },
  {
    icon: BookOpen,
    title: "MSME Credit Guide",
    description: "Comprehensive guide to understanding MSME credit assessment and risk management.",
    type: "White Paper",
    link: "#",
    color: "bg-emerald-500"
  },
  {
    icon: TrendingUp,
    title: "Industry Insights 2024",
    description: "Latest trends and analytics in MSME lending and financial inclusion in India.",
    type: "Report",
    link: "#",
    color: "bg-purple-500"
  },
  {
    icon: Users,
    title: "Success Stories",
    description: "Real-world case studies from banks and NBFCs using Credit Chakra platform.",
    type: "Case Studies",
    link: "#",
    color: "bg-orange-500"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

export default function Resources() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Resources & Insights
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Access comprehensive documentation, industry insights, and success stories to maximize 
            your Credit Chakra implementation.
          </p>
        </motion.div>

        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {resources.map((resource, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="h-full border-gray-200 hover:border-emerald-300 hover:shadow-premium transition-all duration-300 group">
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className={`w-12 h-12 ${resource.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <resource.icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <span className="text-xs font-medium text-emerald-600 uppercase tracking-wide">
                        {resource.type}
                      </span>
                    </div>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 group-hover:text-emerald-600 transition-colors">
                    {resource.title}
                  </h3>
                  
                  <p className="text-gray-600 text-sm leading-relaxed mb-6">
                    {resource.description}
                  </p>
                  
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      className="flex-1 bg-emerald-500 hover:bg-emerald-600 text-white"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className="border-emerald-300 text-emerald-600 hover:bg-emerald-50"
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Resources */}
        <motion.div 
          className="bg-white rounded-2xl p-8 shadow-lg border border-gray-200"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <BookOpen className="w-8 h-8 text-emerald-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Developer Hub</h3>
              <p className="text-gray-600 text-sm mb-4">
                Complete SDKs, code samples, and integration guides for developers.
              </p>
              <Button variant="outline" size="sm" className="border-emerald-300 text-emerald-600">
                Explore Hub
              </Button>
            </div>
            
            <div>
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Users className="w-8 h-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Community</h3>
              <p className="text-gray-600 text-sm mb-4">
                Join our community of financial institutions and fintech innovators.
              </p>
              <Button variant="outline" size="sm" className="border-blue-300 text-blue-600">
                Join Community
              </Button>
            </div>
            
            <div>
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Webinars</h3>
              <p className="text-gray-600 text-sm mb-4">
                Regular webinars on MSME trends, regulatory updates, and best practices.
              </p>
              <Button variant="outline" size="sm" className="border-purple-300 text-purple-600">
                View Schedule
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
