"use client"

import { motion } from "framer-motion"
import { Card } from "@/components/ui/card"

export default function PlatformPreview() {
  return (
    <section className="py-20 bg-gradient-to-br from-emerald-50 to-green-50">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            See Credit Chakra in Action
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience our intuitive dashboard that transforms complex financial data 
            into actionable insights for smarter lending decisions.
          </p>
        </motion.div>

        {/* Main Dashboard Preview */}
        <motion.div 
          className="mb-16"
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 1, ease: "easeOut" }}
          viewport={{ once: true }}
        >
          <Card className="p-8 shadow-premium">
            <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8">
              {/* Dashboard Header */}
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-emerald-500 rounded-xl flex items-center justify-center">
                    <div className="w-6 h-6 bg-white rounded-md"></div>
                  </div>
                  <div>
                    <div className="h-6 w-32 bg-gray-300 rounded mb-1"></div>
                    <div className="h-4 w-24 bg-gray-200 rounded"></div>
                  </div>
                </div>
                <div className="flex space-x-3">
                  <div className="h-10 w-24 bg-emerald-500 rounded-xl"></div>
                  <div className="h-10 w-10 bg-gray-200 rounded-xl"></div>
                </div>
              </div>

              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                {[
                  { color: "bg-emerald-500", label: "Portfolio Health" },
                  { color: "bg-green-500", label: "Active MSMEs" },
                  { color: "bg-amber-500", label: "Risk Alerts" },
                  { color: "bg-blue-500", label: "Compliance Score" }
                ].map((metric, index) => (
                  <motion.div 
                    key={index}
                    className="bg-white rounded-xl p-6 shadow-lg"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    <div className={`w-12 h-12 ${metric.color} rounded-lg mb-4`}></div>
                    <div className="h-8 w-16 bg-gray-800 rounded mb-2"></div>
                    <div className="h-4 w-20 bg-gray-300 rounded"></div>
                  </motion.div>
                ))}
              </div>

              {/* Main Chart Area */}
              <div className="grid lg:grid-cols-3 gap-8">
                <div className="lg:col-span-2">
                  <div className="bg-white rounded-xl p-6 shadow-lg">
                    <div className="flex items-center justify-between mb-6">
                      <div className="h-6 w-32 bg-gray-300 rounded"></div>
                      <div className="flex space-x-2">
                        <div className="h-8 w-16 bg-emerald-100 rounded-lg"></div>
                        <div className="h-8 w-16 bg-gray-100 rounded-lg"></div>
                      </div>
                    </div>
                    <div className="h-64 bg-gradient-to-t from-emerald-50 to-transparent rounded-lg flex items-end justify-between px-4 pb-4">
                      {[...Array(12)].map((_, i) => (
                        <motion.div 
                          key={i}
                          className="bg-emerald-400 rounded-t w-8"
                          style={{ height: `${Math.random() * 80 + 20}%` }}
                          initial={{ height: 0 }}
                          whileInView={{ height: `${Math.random() * 80 + 20}%` }}
                          transition={{ duration: 1, delay: i * 0.1 }}
                          viewport={{ once: true }}
                        ></motion.div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Risk Distribution */}
                  <div className="bg-white rounded-xl p-6 shadow-lg">
                    <div className="h-5 w-24 bg-gray-300 rounded mb-4"></div>
                    <div className="space-y-3">
                      {[
                        { color: "bg-green-500", width: "w-3/4" },
                        { color: "bg-amber-500", width: "w-1/2" },
                        { color: "bg-red-500", width: "w-1/4" }
                      ].map((bar, index) => (
                        <div key={index} className="flex items-center space-x-3">
                          <div className={`w-3 h-3 ${bar.color} rounded-full`}></div>
                          <div className="flex-1 bg-gray-100 rounded-full h-2">
                            <motion.div 
                              className={`h-2 ${bar.color} rounded-full ${bar.width}`}
                              initial={{ width: 0 }}
                              whileInView={{ width: bar.width }}
                              transition={{ duration: 1, delay: 0.5 + index * 0.2 }}
                              viewport={{ once: true }}
                            ></motion.div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Recent Activity */}
                  <div className="bg-white rounded-xl p-6 shadow-lg">
                    <div className="h-5 w-28 bg-gray-300 rounded mb-4"></div>
                    <div className="space-y-3">
                      {[...Array(4)].map((_, i) => (
                        <motion.div 
                          key={i}
                          className="flex items-center space-x-3"
                          initial={{ opacity: 0, x: 20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: 0.8 + i * 0.1 }}
                          viewport={{ once: true }}
                        >
                          <div className="w-8 h-8 bg-emerald-100 rounded-full"></div>
                          <div className="flex-1">
                            <div className="h-3 w-full bg-gray-200 rounded mb-1"></div>
                            <div className="h-2 w-2/3 bg-gray-100 rounded"></div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </motion.div>

        {/* Feature Highlights */}
        <motion.div 
          className="grid md:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          {[
            "Real-time Risk Monitoring",
            "Predictive Analytics",
            "Compliance Tracking"
          ].map((feature, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <div className="w-8 h-8 bg-white rounded-lg"></div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature}</h3>
              <p className="text-gray-600">Advanced capabilities that drive better outcomes</p>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
