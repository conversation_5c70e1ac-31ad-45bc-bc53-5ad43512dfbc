"use client"

import { motion, useInView } from "framer-motion"
import { useRef, useEffect, useState } from "react"

interface MetricProps {
  value: number
  suffix: string
  label: string
  description: string
  duration?: number
}

function AnimatedCounter({ value, suffix, label, description, duration = 2 }: MetricProps) {
  const [count, setCount] = useState(0)
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true })

  useEffect(() => {
    if (isInView) {
      let startTime: number
      let animationFrame: number

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp
        const progress = Math.min((timestamp - startTime) / (duration * 1000), 1)
        
        setCount(Math.floor(progress * value))
        
        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate)
        }
      }

      animationFrame = requestAnimationFrame(animate)
      return () => cancelAnimationFrame(animationFrame)
    }
  }, [isInView, value, duration])

  return (
    <motion.div 
      ref={ref}
      className="text-center"
      initial={{ opacity: 0, y: 30 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
    >
      <div className="text-6xl lg:text-7xl font-bold text-gradient mb-4">
        {count}{suffix}
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">{label}</h3>
      <p className="text-gray-600 max-w-xs mx-auto">{description}</p>
    </motion.div>
  )
}

const metrics = [
  {
    value: 20,
    suffix: "%",
    label: "Faster Risk Detection",
    description: "Reduce time to identify potential defaults with AI-powered early warning systems"
  },
  {
    value: 30,
    suffix: "%",
    label: "Reduction in NPLs",
    description: "Significant decrease in non-performing loans through predictive analytics"
  },
  {
    value: 50,
    suffix: "K+",
    label: "MSMEs Profiled",
    description: "Comprehensive credit profiles created for micro, small, and medium enterprises"
  },
  {
    value: 95,
    suffix: "%",
    label: "Accuracy Rate",
    description: "Industry-leading precision in credit risk assessment and scoring models"
  }
]

export default function ImpactMetrics() {
  return (
    <section className="py-20 bg-gradient-to-br from-emerald-900 via-green-800 to-emerald-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold mb-6">
            Measurable Impact on MSME Lending
          </h2>
          <p className="text-xl text-emerald-100 max-w-3xl mx-auto">
            Our platform delivers quantifiable results that transform lending operations 
            and drive sustainable growth for financial institutions.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-12">
          {metrics.map((metric, index) => (
            <AnimatedCounter 
              key={index}
              value={metric.value}
              suffix={metric.suffix}
              label={metric.label}
              description={metric.description}
              duration={2 + index * 0.2}
            />
          ))}
        </div>

        {/* Additional Impact Stories */}
        <motion.div 
          className="mt-20 grid md:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          {[
            {
              title: "Processing Speed",
              description: "Credit assessments completed in minutes instead of days",
              highlight: "10x Faster"
            },
            {
              title: "Data Integration",
              description: "Seamless connection with multiple financial data sources",
              highlight: "50+ APIs"
            },
            {
              title: "Cost Reduction",
              description: "Lower operational costs through automated risk assessment",
              highlight: "40% Savings"
            }
          ].map((story, index) => (
            <motion.div 
              key={index}
              className="text-center p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20"
              initial={{ opacity: 0, scale: 0.95 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-3xl font-bold text-accent-gold mb-3">
                {story.highlight}
              </div>
              <h3 className="text-lg font-semibold mb-2">{story.title}</h3>
              <p className="text-emerald-100 text-sm">{story.description}</p>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div 
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl font-bold mb-4">
            Ready to Transform Your MSME Lending?
          </h3>
          <p className="text-emerald-100 mb-8 max-w-2xl mx-auto">
            Join leading financial institutions who trust Credit Chakra to drive 
            their digital transformation and improve lending outcomes.
          </p>
          <motion.button 
            className="bg-accent-gold text-gray-900 px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-yellow-400 transition-colors duration-300 shadow-lg hover:shadow-xl"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Schedule a Demo
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}
