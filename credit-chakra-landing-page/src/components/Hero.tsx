"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import Image from "next/image"

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Animated Gradient Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-emerald-500 via-green-600 to-emerald-800 animate-gradient" />
      
      {/* Content Container */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 py-20">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          
          {/* Left Column - Text Content */}
          <div className="text-white space-y-8">
            <motion.h1
              className="text-4xl lg:text-6xl font-bold leading-tight"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              Smarter MSME Credit Intelligence for the{" "}
              <span className="text-accent-gold">Future</span>
            </motion.h1>
            
            <motion.p 
              className="text-xl lg:text-2xl text-emerald-100 leading-relaxed max-w-2xl"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
            >
              AI-powered scoring, monitoring, and early-warning systems — helping lenders 
              and policymakers accelerate MSME growth.
            </motion.p>
            
            <motion.div 
              className="flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.4, ease: "easeOut" }}
            >
              <Button 
                size="lg" 
                className="bg-emerald-500 hover:bg-emerald-600 text-white font-semibold px-8 py-4 text-lg shadow-premium hover:shadow-premium-hover"
              >
                Request a Demo
              </Button>
              <Button 
                size="lg" 
                variant="secondary"
                className="bg-white text-emerald-600 hover:bg-emerald-50 font-semibold px-8 py-4 text-lg"
              >
                Explore Platform
              </Button>
            </motion.div>
          </div>
          
          {/* Right Column - Dashboard Mockup */}
          <motion.div 
            className="relative"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.3, ease: "easeOut" }}
          >
            <div className="glass-effect rounded-3xl p-8 shadow-premium">
              {/* Dashboard Mockup Placeholder */}
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <div className="space-y-4">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div className="h-8 w-32 bg-emerald-500 rounded-lg"></div>
                    <div className="flex space-x-2">
                      <div className="h-6 w-6 bg-emerald-200 rounded-full"></div>
                      <div className="h-6 w-6 bg-emerald-300 rounded-full"></div>
                      <div className="h-6 w-6 bg-emerald-400 rounded-full"></div>
                    </div>
                  </div>
                  
                  {/* Stats Cards */}
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-emerald-50 p-4 rounded-xl">
                      <div className="h-4 w-16 bg-emerald-200 rounded mb-2"></div>
                      <div className="h-6 w-12 bg-emerald-500 rounded"></div>
                    </div>
                    <div className="bg-green-50 p-4 rounded-xl">
                      <div className="h-4 w-16 bg-green-200 rounded mb-2"></div>
                      <div className="h-6 w-12 bg-green-500 rounded"></div>
                    </div>
                    <div className="bg-accent-gold/20 p-4 rounded-xl">
                      <div className="h-4 w-16 bg-accent-gold/40 rounded mb-2"></div>
                      <div className="h-6 w-12 bg-accent-gold rounded"></div>
                    </div>
                  </div>
                  
                  {/* Chart Area */}
                  <div className="bg-gray-50 rounded-xl p-6">
                    <div className="h-4 w-24 bg-gray-300 rounded mb-4"></div>
                    <div className="flex items-end space-x-2 h-32">
                      {[...Array(8)].map((_, i) => (
                        <div 
                          key={i}
                          className="bg-emerald-400 rounded-t flex-1"
                          style={{ height: `${Math.random() * 80 + 20}%` }}
                        ></div>
                      ))}
                    </div>
                  </div>
                  
                  {/* List Items */}
                  <div className="space-y-3">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="flex items-center space-x-3">
                        <div className="h-3 w-3 bg-emerald-500 rounded-full"></div>
                        <div className="h-3 flex-1 bg-gray-200 rounded"></div>
                        <div className="h-3 w-16 bg-emerald-300 rounded"></div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            {/* Floating Elements */}
            <motion.div 
              className="absolute -top-4 -right-4 w-16 h-16 bg-accent-gold rounded-full opacity-80"
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
            />
            <motion.div 
              className="absolute -bottom-6 -left-6 w-12 h-12 bg-emerald-300 rounded-full opacity-60"
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut", delay: 1 }}
            />
          </motion.div>
        </div>
      </div>
      
      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" className="w-full h-20 fill-white">
          <path d="M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,48C672,43,768,53,864,64C960,75,1056,85,1152,80C1248,75,1344,53,1392,42.7L1440,32L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z"></path>
        </svg>
      </div>
    </section>
  )
}
