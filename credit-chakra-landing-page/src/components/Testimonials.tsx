"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Star, Quote } from "lucide-react"

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    title: "Chief Risk Officer",
    company: "National Development Bank",
    content: "Credit Chakra has revolutionized our MSME lending process. The AI-powered risk assessment has reduced our NPLs by 35% while increasing our loan approval speed by 60%.",
    rating: 5,
    avatar: "RK"
  },
  {
    name: "<PERSON><PERSON>",
    title: "Director of Financial Inclusion",
    company: "Ministry of MSME",
    content: "The platform provides invaluable insights into MSME ecosystem health. We can now track policy impact in real-time and make data-driven decisions for better outcomes.",
    rating: 5,
    avatar: "PS"
  },
  {
    name: "<PERSON><PERSON>",
    title: "CTO",
    company: "FinTech Solutions Ltd",
    content: "Integration was seamless and the API response times are exceptional. Credit Chakra's white-label solution helped us launch our lending product 3 months ahead of schedule.",
    rating: 5,
    avatar: "AP"
  }
]

const partners = [
  { name: "Reserve Bank of India", logo: "RBI" },
  { name: "State Bank of India", logo: "SBI" },
  { name: "HDFC Bank", logo: "HDFC" },
  { name: "ICICI Bank", logo: "ICICI" },
  { name: "Axis Bank", logo: "AXIS" },
  { name: "Ministry of MSME", logo: "MSME" },
  { name: "SIDBI", logo: "SIDBI" },
  { name: "MUDRA", logo: "MUDRA" }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

export default function Testimonials() {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-emerald-50">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Trusted by Industry Leaders
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See what our partners and clients say about their experience with Credit Chakra's 
            innovative MSME credit intelligence platform.
          </p>
        </motion.div>

        {/* Testimonials */}
        <motion.div 
          className="grid md:grid-cols-3 gap-8 mb-20"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {testimonials.map((testimonial, index) => (
            <motion.div key={index} variants={itemVariants}>
              <Card className="h-full border-emerald-100 hover:border-emerald-300 hover:shadow-premium transition-all duration-300 group">
                <CardContent className="p-8">
                  {/* Quote Icon */}
                  <div className="mb-6">
                    <Quote className="w-8 h-8 text-emerald-500 group-hover:text-emerald-600 transition-colors" />
                  </div>
                  
                  {/* Rating */}
                  <div className="flex space-x-1 mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 fill-accent-gold text-accent-gold" />
                    ))}
                  </div>
                  
                  {/* Content */}
                  <p className="text-gray-700 leading-relaxed mb-6 italic">
                    "{testimonial.content}"
                  </p>
                  
                  {/* Author */}
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center text-white font-semibold">
                      {testimonial.avatar}
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.title}</div>
                      <div className="text-sm text-emerald-600 font-medium">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Partner Logos */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Partnering with Leading Institutions
            </h3>
            <p className="text-gray-600">
              Trusted by banks, NBFCs, government agencies, and fintech companies across India
            </p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center">
            {partners.map((partner, index) => (
              <motion.div 
                key={index}
                className="flex items-center justify-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-20 h-20 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:shadow-xl hover:scale-105 transition-all duration-300 group">
                  <div className="text-center">
                    <div className="text-xs font-bold text-emerald-600 group-hover:text-emerald-700">
                      {partner.logo}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div 
          className="mt-16 grid md:grid-cols-4 gap-8 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          {[
            { label: "Enterprise Clients", value: "100+" },
            { label: "Data Security", value: "ISO 27001" },
            { label: "Uptime SLA", value: "99.9%" },
            { label: "Support", value: "24/7" }
          ].map((indicator, index) => (
            <motion.div 
              key={index}
              className="space-y-2"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.7 + index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-3xl font-bold text-gradient">{indicator.value}</div>
              <div className="text-gray-600 font-medium">{indicator.label}</div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}
