"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { Building2, Landmark, Smartphone, CheckCircle } from "lucide-react"

const useCases = {
  banks: {
    title: "Banks & NBFCs",
    icon: Building2,
    description: "Enhance your lending portfolio with AI-driven risk assessment and real-time monitoring capabilities.",
    benefits: [
      "Automated credit scoring for faster loan approvals",
      "Real-time portfolio monitoring and risk alerts",
      "Regulatory compliance and reporting automation",
      "Reduced NPLs through predictive analytics",
      "Enhanced customer onboarding experience",
      "Integration with existing core banking systems"
    ],
    stats: [
      { label: "Faster Processing", value: "75%" },
      { label: "Risk Reduction", value: "40%" },
      { label: "Cost Savings", value: "60%" }
    ]
  },
  government: {
    title: "Government Agencies",
    icon: Landmark,
    description: "Support policy implementation and MSME development with comprehensive data insights and monitoring tools.",
    benefits: [
      "Policy impact measurement and analysis",
      "MSME ecosystem health monitoring",
      "Subsidy and scheme effectiveness tracking",
      "Regional economic development insights",
      "Compliance monitoring and enforcement",
      "Data-driven policy recommendations"
    ],
    stats: [
      { label: "Policy Reach", value: "85%" },
      { label: "Monitoring Efficiency", value: "70%" },
      { label: "Data Accuracy", value: "95%" }
    ]
  },
  fintechs: {
    title: "Fintechs",
    icon: Smartphone,
    description: "Power your fintech solutions with advanced credit intelligence and seamless API integrations.",
    benefits: [
      "White-label credit scoring solutions",
      "API-first architecture for easy integration",
      "Scalable infrastructure for rapid growth",
      "Advanced analytics and machine learning",
      "Custom risk models and parameters",
      "Real-time decision engines"
    ],
    stats: [
      { label: "API Response Time", value: "<100ms" },
      { label: "Scalability", value: "99.9%" },
      { label: "Integration Speed", value: "2 weeks" }
    ]
  }
}

export default function UseCasesTabs() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Tailored Solutions for Every Stakeholder
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Credit Chakra adapts to your specific needs, whether you're a financial institution, 
            government agency, or fintech innovator.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Tabs defaultValue="banks" className="w-full">
            <TabsList className="grid w-full grid-cols-3 mb-12 h-16">
              {Object.entries(useCases).map(([key, useCase]) => {
                const IconComponent = useCase.icon
                return (
                  <TabsTrigger 
                    key={key} 
                    value={key}
                    className="flex items-center space-x-2 text-base font-medium h-full"
                  >
                    <IconComponent className="w-5 h-5" />
                    <span>{useCase.title}</span>
                  </TabsTrigger>
                )
              })}
            </TabsList>

            {Object.entries(useCases).map(([key, useCase]) => (
              <TabsContent key={key} value={key}>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <div className="grid lg:grid-cols-2 gap-12 items-center">
                    {/* Content */}
                    <div>
                      <div className="flex items-center space-x-4 mb-6">
                        <div className="w-16 h-16 bg-emerald-500 rounded-2xl flex items-center justify-center">
                          <useCase.icon className="w-8 h-8 text-white" />
                        </div>
                        <h3 className="text-3xl font-bold text-gray-900">
                          {useCase.title}
                        </h3>
                      </div>
                      
                      <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                        {useCase.description}
                      </p>

                      <div className="space-y-4">
                        <h4 className="text-xl font-semibold text-gray-900 mb-4">
                          Key Benefits:
                        </h4>
                        {useCase.benefits.map((benefit, index) => (
                          <motion.div 
                            key={index}
                            className="flex items-start space-x-3"
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.5, delay: index * 0.1 }}
                          >
                            <CheckCircle className="w-6 h-6 text-emerald-500 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-700">{benefit}</span>
                          </motion.div>
                        ))}
                      </div>
                    </div>

                    {/* Stats and Visual */}
                    <div className="space-y-8">
                      {/* Stats Cards */}
                      <div className="grid grid-cols-1 gap-6">
                        {useCase.stats.map((stat, index) => (
                          <motion.div
                            key={index}
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                          >
                            <Card className="p-6 border-emerald-100 hover:border-emerald-300 transition-colors">
                              <CardContent className="p-0">
                                <div className="flex items-center justify-between">
                                  <span className="text-gray-600 font-medium">
                                    {stat.label}
                                  </span>
                                  <span className="text-3xl font-bold text-gradient">
                                    {stat.value}
                                  </span>
                                </div>
                              </CardContent>
                            </Card>
                          </motion.div>
                        ))}
                      </div>

                      {/* Illustration Placeholder */}
                      <motion.div
                        className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl p-8"
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.6, delay: 0.5 }}
                      >
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div className="h-6 w-32 bg-emerald-300 rounded"></div>
                            <div className="h-6 w-6 bg-emerald-500 rounded-full"></div>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="h-20 bg-emerald-200 rounded-xl"></div>
                            <div className="h-20 bg-green-200 rounded-xl"></div>
                          </div>
                          <div className="space-y-2">
                            {[...Array(3)].map((_, i) => (
                              <div key={i} className="flex items-center space-x-3">
                                <div className="h-3 w-3 bg-emerald-400 rounded-full"></div>
                                <div className="h-3 flex-1 bg-emerald-100 rounded"></div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              </TabsContent>
            ))}
          </Tabs>
        </motion.div>
      </div>
    </section>
  )
}
