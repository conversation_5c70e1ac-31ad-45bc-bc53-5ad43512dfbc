"use client"

import { motion } from "framer-motion"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Activity, Brain, AlertTriangle, Target } from "lucide-react"

const valueProps = [
  {
    icon: Activity,
    title: "Real-Time Credit Monitoring",
    description: "Continuous tracking of MSME financial health with instant alerts on risk changes and portfolio performance metrics.",
    color: "text-emerald-600",
    bgColor: "bg-emerald-50"
  },
  {
    icon: Brain,
    title: "AI-Driven Scoring Models",
    description: "Advanced machine learning algorithms that analyze multiple data sources for accurate credit risk assessment.",
    color: "text-green-600",
    bgColor: "bg-green-50"
  },
  {
    icon: AlertTriangle,
    title: "Early-Warning Insights",
    description: "Predictive analytics that identify potential defaults before they happen, enabling proactive risk management.",
    color: "text-amber-600",
    bgColor: "bg-amber-50"
  },
  {
    icon: Target,
    title: "Policy & ESG Alignment",
    description: "Comprehensive compliance tracking and ESG scoring to support sustainable lending practices and regulatory requirements.",
    color: "text-blue-600",
    bgColor: "bg-blue-50"
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
}

const cardVariants = {
  hidden: { 
    opacity: 0, 
    y: 30 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

export default function ValuePropositions() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Transforming MSME Credit Intelligence
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our comprehensive platform delivers the insights and tools you need to make smarter 
            lending decisions and drive sustainable growth.
          </p>
        </motion.div>

        <motion.div 
          className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {valueProps.map((prop, index) => {
            const IconComponent = prop.icon
            return (
              <motion.div key={index} variants={cardVariants}>
                <Card className="h-full border-emerald-100 hover:border-emerald-300 transition-all duration-300 group">
                  <CardHeader className="text-center pb-4">
                    <div className={`w-16 h-16 ${prop.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      <IconComponent className={`w-8 h-8 ${prop.color}`} />
                    </div>
                    <CardTitle className="text-xl font-semibold text-gray-900">
                      {prop.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="text-center">
                    <p className="text-gray-600 leading-relaxed">
                      {prop.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Stats Section */}
        <motion.div 
          className="mt-20 grid md:grid-cols-3 gap-8 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className="space-y-2">
            <div className="text-4xl font-bold text-gradient">99.9%</div>
            <div className="text-gray-600">System Uptime</div>
          </div>
          <div className="space-y-2">
            <div className="text-4xl font-bold text-gradient">50K+</div>
            <div className="text-gray-600">MSMEs Analyzed</div>
          </div>
          <div className="space-y-2">
            <div className="text-4xl font-bold text-gradient">30%</div>
            <div className="text-gray-600">Risk Reduction</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
