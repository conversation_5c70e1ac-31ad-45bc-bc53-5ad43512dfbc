"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { UserPlus, BarChart3, Shield } from "lucide-react"

const steps = [
  {
    icon: UserPlus,
    title: "Connect & Onboard",
    description: "Seamlessly integrate with existing systems and onboard MSMEs through our secure API connections and data aggregation platform.",
    features: [
      "API Integration",
      "Data Aggregation",
      "Secure Onboarding",
      "System Compatibility"
    ]
  },
  {
    icon: BarChart3,
    title: "Analyze & Score",
    description: "Our AI algorithms analyze multiple data sources to generate comprehensive credit scores and risk assessments in real-time.",
    features: [
      "AI-Powered Analysis",
      "Multi-Source Data",
      "Real-time Scoring",
      "Risk Assessment"
    ]
  },
  {
    icon: Shield,
    title: "Monitor & Act",
    description: "Continuous monitoring with early warning systems and actionable insights to help you make informed lending decisions.",
    features: [
      "Continuous Monitoring",
      "Early Warnings",
      "Actionable Insights",
      "Decision Support"
    ]
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3
    }
  }
}

const stepVariants = {
  hidden: { 
    opacity: 0, 
    y: 50 
  },
  visible: { 
    opacity: 1, 
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
}

export default function HowItWorks() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div 
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            How Credit Chakra Works
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our streamlined process transforms complex financial data into actionable 
            insights through three simple steps.
          </p>
        </motion.div>

        <motion.div 
          className="grid lg:grid-cols-3 gap-8"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          {steps.map((step, index) => {
            const IconComponent = step.icon
            return (
              <motion.div key={index} variants={stepVariants} className="relative">
                {/* Connector Line */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-emerald-400 to-green-600 z-10"></div>
                )}
                
                <Card className="h-full bg-gradient-to-tr from-emerald-400 to-green-600 text-white border-none shadow-premium hover:shadow-premium-hover transition-all duration-300 group">
                  <CardContent className="p-8">
                    {/* Step Number */}
                    <div className="flex items-center justify-between mb-6">
                      <div className="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div className="text-6xl font-bold text-white/20">
                        {String(index + 1).padStart(2, '0')}
                      </div>
                    </div>
                    
                    {/* Content */}
                    <h3 className="text-2xl font-bold mb-4">{step.title}</h3>
                    <p className="text-emerald-50 mb-6 leading-relaxed">
                      {step.description}
                    </p>
                    
                    {/* Features List */}
                    <div className="space-y-3">
                      {step.features.map((feature, featureIndex) => (
                        <motion.div 
                          key={featureIndex}
                          className="flex items-center space-x-3"
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ 
                            duration: 0.5, 
                            delay: 0.5 + (index * 0.3) + (featureIndex * 0.1) 
                          }}
                          viewport={{ once: true }}
                        >
                          <div className="w-2 h-2 bg-accent-gold rounded-full"></div>
                          <span className="text-emerald-50 font-medium">{feature}</span>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Process Flow Visualization */}
        <motion.div 
          className="mt-20 bg-gradient-to-r from-emerald-50 to-green-50 rounded-3xl p-12"
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              End-to-End Process Flow
            </h3>
            <p className="text-gray-600">
              From data ingestion to actionable insights in minutes, not days
            </p>
          </div>
          
          <div className="flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0 lg:space-x-8">
            {[
              "Data Sources",
              "AI Processing",
              "Risk Analysis",
              "Insights Dashboard",
              "Decision Support"
            ].map((stage, index) => (
              <motion.div 
                key={index}
                className="flex flex-col items-center"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-16 h-16 bg-emerald-500 rounded-2xl flex items-center justify-center mb-3 shadow-lg">
                  <div className="w-8 h-8 bg-white rounded-lg"></div>
                </div>
                <span className="text-sm font-medium text-gray-700 text-center">
                  {stage}
                </span>
                {index < 4 && (
                  <div className="hidden lg:block w-8 h-0.5 bg-emerald-300 mt-8 absolute translate-x-12"></div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
