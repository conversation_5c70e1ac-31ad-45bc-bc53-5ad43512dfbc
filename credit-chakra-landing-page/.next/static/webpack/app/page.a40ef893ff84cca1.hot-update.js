/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/menu.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Menu; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */\n\n\n\nconst Menu = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Menu\", [\n  [\"line\", { x1: \"4\", x2: \"20\", y1: \"12\", y2: \"12\", key: \"1e0a9i\" }],\n  [\"line\", { x1: \"4\", x2: \"20\", y1: \"6\", y2: \"6\", key: \"1owob3\" }],\n  [\"line\", { x1: \"4\", x2: \"20\", y1: \"18\", y2: \"18\", key: \"yk5zj1\" }]\n]);\n\n\n//# sourceMappingURL=menu.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbWVudS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGFBQWEsZ0VBQWdCO0FBQzdCLGFBQWEsc0RBQXNEO0FBQ25FLGFBQWEsb0RBQW9EO0FBQ2pFLGFBQWEsc0RBQXNEO0FBQ25FOztBQUUyQjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL21lbnUuanM/YWM1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGx1Y2lkZS1yZWFjdCB2MC4yOTIuMCAtIElTQ1xuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBNZW51ID0gY3JlYXRlTHVjaWRlSWNvbihcIk1lbnVcIiwgW1xuICBbXCJsaW5lXCIsIHsgeDE6IFwiNFwiLCB4MjogXCIyMFwiLCB5MTogXCIxMlwiLCB5MjogXCIxMlwiLCBrZXk6IFwiMWUwYTlpXCIgfV0sXG4gIFtcImxpbmVcIiwgeyB4MTogXCI0XCIsIHgyOiBcIjIwXCIsIHkxOiBcIjZcIiwgeTI6IFwiNlwiLCBrZXk6IFwiMW93b2IzXCIgfV0sXG4gIFtcImxpbmVcIiwgeyB4MTogXCI0XCIsIHgyOiBcIjIwXCIsIHkxOiBcIjE4XCIsIHkyOiBcIjE4XCIsIGtleTogXCJ5azV6ajFcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IE1lbnUgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bWVudS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js":
/*!*******************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ X; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * lucide-react v0.292.0 - ISC\n */\n\n\n\nconst X = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"X\", [\n  [\"path\", { d: \"M18 6 6 18\", key: \"1bl5f8\" }],\n  [\"path\", { d: \"m6 6 12 12\", key: \"d8bk6v\" }]\n]);\n\n\n//# sourceMappingURL=x.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMveC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELFVBQVUsZ0VBQWdCO0FBQzFCLGFBQWEsZ0NBQWdDO0FBQzdDLGFBQWEsZ0NBQWdDO0FBQzdDOztBQUV3QjtBQUN4QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL3guanM/NDAyNCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGx1Y2lkZS1yZWFjdCB2MC4yOTIuMCAtIElTQ1xuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBYID0gY3JlYXRlTHVjaWRlSWNvbihcIlhcIiwgW1xuICBbXCJwYXRoXCIsIHsgZDogXCJNMTggNiA2IDE4XCIsIGtleTogXCIxYmw1ZjhcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwibTYgNiAxMiAxMlwiLCBrZXk6IFwiZDhiazZ2XCIgfV1cbl0pO1xuXG5leHBvcnQgeyBYIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXguanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FCTABanner.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHero.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHowItWorks.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FImpactMetrics.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FNavbar.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FPlatformPreview.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FTestimonials.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FUseCasesTabs.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FValuePropositions.tsx&server=false!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FCTABanner.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHero.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHowItWorks.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FImpactMetrics.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FNavbar.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FPlatformPreview.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FTestimonials.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FUseCasesTabs.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FValuePropositions.tsx&server=false! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CTABanner.tsx */ \"(app-pages-browser)/./src/components/CTABanner.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(app-pages-browser)/./src/components/Hero.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/HowItWorks.tsx */ \"(app-pages-browser)/./src/components/HowItWorks.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ImpactMetrics.tsx */ \"(app-pages-browser)/./src/components/ImpactMetrics.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Navbar.tsx */ \"(app-pages-browser)/./src/components/Navbar.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PlatformPreview.tsx */ \"(app-pages-browser)/./src/components/PlatformPreview.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Testimonials.tsx */ \"(app-pages-browser)/./src/components/Testimonials.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/UseCasesTabs.tsx */ \"(app-pages-browser)/./src/components/UseCasesTabs.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ValuePropositions.tsx */ \"(app-pages-browser)/./src/components/ValuePropositions.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FCTABanner.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHero.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHowItWorks.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FImpactMetrics.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FNavbar.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FPlatformPreview.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FTestimonials.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FUseCasesTabs.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FValuePropositions.tsx&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Navbar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst navigation = [\n    {\n        name: \"Platform\",\n        href: \"#platform\"\n    },\n    {\n        name: \"Solutions\",\n        href: \"#solutions\"\n    },\n    {\n        name: \"Use Cases\",\n        href: \"#use-cases\"\n    },\n    {\n        name: \"About\",\n        href: \"#about\"\n    },\n    {\n        name: \"Resources\",\n        href: \"#resources\"\n    }\n];\nfunction Navbar() {\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleScroll = ()=>{\n            setIsScrolled(window.scrollY > 10);\n        };\n        window.addEventListener(\"scroll\", handleScroll);\n        return ()=>window.removeEventListener(\"scroll\", handleScroll);\n    }, []);\n    const scrollToSection = (href)=>{\n        if (href.startsWith(\"#\")) {\n            const element = document.querySelector(href);\n            if (element) {\n                element.scrollIntoView({\n                    behavior: \"smooth\"\n                });\n            }\n        }\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.nav, {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? \"bg-white/95 backdrop-blur-md shadow-lg border-b border-emerald-100\" : \"bg-transparent\"),\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center h-16 lg:h-20\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            className: \"flex items-center space-x-3\",\n                            whileHover: {\n                                scale: 1.05\n                            },\n                            transition: {\n                                duration: 0.2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-white font-bold text-lg\",\n                                        children: \"CC\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-xl \".concat(isScrolled ? \"text-gray-900\" : \"text-white\"),\n                                            children: \"Credit Chakra\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs \".concat(isScrolled ? \"text-emerald-600\" : \"text-emerald-200\"),\n                                            children: \"MSME Intelligence\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-8\",\n                            children: navigation.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    onClick: ()=>scrollToSection(item.href),\n                                    className: \"text-sm font-medium transition-colors duration-200 hover:text-emerald-500 \".concat(isScrolled ? \"text-gray-700\" : \"text-white/90\"),\n                                    initial: {\n                                        opacity: 0,\n                                        y: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: index * 0.1\n                                    },\n                                    whileHover: {\n                                        scale: 1.05\n                                    },\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden lg:flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.3\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"font-medium \".concat(isScrolled ? \"text-gray-700 hover:text-emerald-600 hover:bg-emerald-50\" : \"text-white hover:text-emerald-200 hover:bg-white/10\"),\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-emerald-500 hover:bg-emerald-600 text-white font-medium px-6 shadow-lg hover:shadow-xl transition-all duration-200\",\n                                        children: \"Request Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                onClick: ()=>setIsOpen(!isOpen),\n                                className: \"p-2 rounded-lg transition-colors \".concat(isScrolled ? \"text-gray-700 hover:bg-gray-100\" : \"text-white hover:bg-white/10\"),\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 25\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    className: \"lg:hidden bg-white border-t border-gray-200 shadow-lg\",\n                    initial: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    animate: {\n                        opacity: 1,\n                        height: \"auto\"\n                    },\n                    exit: {\n                        opacity: 0,\n                        height: 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-6 space-y-4\",\n                        children: [\n                            navigation.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                    onClick: ()=>scrollToSection(item.href),\n                                    className: \"block w-full text-left text-gray-700 hover:text-emerald-600 font-medium py-2 transition-colors\",\n                                    initial: {\n                                        opacity: 0,\n                                        x: -20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    transition: {\n                                        duration: 0.3,\n                                        delay: index * 0.1\n                                    },\n                                    children: item.name\n                                }, item.name, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 17\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 space-y-3 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full text-gray-700 border-gray-300 hover:bg-gray-50\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"w-full bg-emerald-500 hover:bg-emerald-600 text-white\",\n                                        children: \"Request Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Navbar.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(Navbar, \"ih+zOjPnCwe91JM2Yua0KTFwF/0=\");\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navbar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs":
/*!************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs ***!
  \************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PopChild: function() { return /* binding */ PopChild; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\n\n/**\n * Measurement functionality has to be within a separate component\n * to leverage snapshot lifecycle.\n */\nclass PopChildMeasure extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    getSnapshotBeforeUpdate(prevProps) {\n        const element = this.props.childRef.current;\n        if (element && prevProps.isPresent && !this.props.isPresent) {\n            const size = this.props.sizeRef.current;\n            size.height = element.offsetHeight || 0;\n            size.width = element.offsetWidth || 0;\n            size.top = element.offsetTop;\n            size.left = element.offsetLeft;\n        }\n        return null;\n    }\n    /**\n     * Required with getSnapshotBeforeUpdate to stop React complaining.\n     */\n    componentDidUpdate() { }\n    render() {\n        return this.props.children;\n    }\n}\nfunction PopChild({ children, isPresent }) {\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const size = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n    });\n    /**\n     * We create and inject a style block so we can apply this explicit\n     * sizing in a non-destructive manner by just deleting the style block.\n     *\n     * We can't apply size via render as the measurement happens\n     * in getSnapshotBeforeUpdate (post-render), likewise if we apply the\n     * styles directly on the DOM node, we might be overwriting\n     * styles set via the style prop.\n     */\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useInsertionEffect)(() => {\n        const { width, height, top, left } = size.current;\n        if (isPresent || !ref.current || !width || !height)\n            return;\n        ref.current.dataset.motionPopId = id;\n        const style = document.createElement(\"style\");\n        document.head.appendChild(style);\n        if (style.sheet) {\n            style.sheet.insertRule(`\n          [data-motion-pop-id=\"${id}\"] {\n            position: absolute !important;\n            width: ${width}px !important;\n            height: ${height}px !important;\n            top: ${top}px !important;\n            left: ${left}px !important;\n          }\n        `);\n        }\n        return () => {\n            document.head.removeChild(style);\n        };\n    }, [isPresent]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(PopChildMeasure, { isPresent: isPresent, childRef: ref, sizeRef: size }, react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, { ref })));\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs":
/*!*****************************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs ***!
  \*****************************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresenceChild: function() { return /* binding */ PresenceChild; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/PresenceContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/PresenceContext.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* harmony import */ var _PopChild_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./PopChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PopChild.mjs\");\n\n\n\n\n\n\nconst PresenceChild = ({ children, initial, isPresent, onExitComplete, custom, presenceAffectsLayout, mode, }) => {\n    const presenceChildren = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_1__.useConstant)(newChildrenMap);\n    const id = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n        id,\n        initial,\n        isPresent,\n        custom,\n        onExitComplete: (childId) => {\n            presenceChildren.set(childId, true);\n            for (const isComplete of presenceChildren.values()) {\n                if (!isComplete)\n                    return; // can stop searching when any is incomplete\n            }\n            onExitComplete && onExitComplete();\n        },\n        register: (childId) => {\n            presenceChildren.set(childId, false);\n            return () => presenceChildren.delete(childId);\n        },\n    }), \n    /**\n     * If the presence of a child affects the layout of the components around it,\n     * we want to make a new context value to ensure they get re-rendered\n     * so they can detect that layout change.\n     */\n    presenceAffectsLayout ? undefined : [isPresent]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n        presenceChildren.forEach((_, key) => presenceChildren.set(key, false));\n    }, [isPresent]);\n    /**\n     * If there's no `motion` components to fire exit animations, we want to remove this\n     * component immediately.\n     */\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n        !isPresent &&\n            !presenceChildren.size &&\n            onExitComplete &&\n            onExitComplete();\n    }, [isPresent]);\n    if (mode === \"popLayout\") {\n        children = react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PopChild_mjs__WEBPACK_IMPORTED_MODULE_2__.PopChild, { isPresent: isPresent }, children);\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_context_PresenceContext_mjs__WEBPACK_IMPORTED_MODULE_3__.PresenceContext.Provider, { value: context }, children));\n};\nfunction newChildrenMap() {\n    return new Map();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvY29tcG9uZW50cy9BbmltYXRlUHJlc2VuY2UvUHJlc2VuY2VDaGlsZC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBK0I7QUFDUTtBQUM2QjtBQUNUO0FBQ2pCOztBQUUxQyx5QkFBeUIsb0ZBQW9GO0FBQzdHLDZCQUE2QixvRUFBVztBQUN4QyxlQUFlLDRDQUFLO0FBQ3BCLG9CQUFvQiw4Q0FBTztBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDhDQUFPO0FBQ1g7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLDRDQUFlO0FBQ25CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsbUJBQW1CLGdEQUFtQixDQUFDLG1EQUFRLElBQUksc0JBQXNCO0FBQ3pFO0FBQ0EsWUFBWSxnREFBbUIsQ0FBQyx5RUFBZSxhQUFhLGdCQUFnQjtBQUM1RTtBQUNBO0FBQ0E7QUFDQTs7QUFFeUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy9jb21wb25lbnRzL0FuaW1hdGVQcmVzZW5jZS9QcmVzZW5jZUNoaWxkLm1qcz83OWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUlkLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgUHJlc2VuY2VDb250ZXh0IH0gZnJvbSAnLi4vLi4vY29udGV4dC9QcmVzZW5jZUNvbnRleHQubWpzJztcbmltcG9ydCB7IHVzZUNvbnN0YW50IH0gZnJvbSAnLi4vLi4vdXRpbHMvdXNlLWNvbnN0YW50Lm1qcyc7XG5pbXBvcnQgeyBQb3BDaGlsZCB9IGZyb20gJy4vUG9wQ2hpbGQubWpzJztcblxuY29uc3QgUHJlc2VuY2VDaGlsZCA9ICh7IGNoaWxkcmVuLCBpbml0aWFsLCBpc1ByZXNlbnQsIG9uRXhpdENvbXBsZXRlLCBjdXN0b20sIHByZXNlbmNlQWZmZWN0c0xheW91dCwgbW9kZSwgfSkgPT4ge1xuICAgIGNvbnN0IHByZXNlbmNlQ2hpbGRyZW4gPSB1c2VDb25zdGFudChuZXdDaGlsZHJlbk1hcCk7XG4gICAgY29uc3QgaWQgPSB1c2VJZCgpO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VNZW1vKCgpID0+ICh7XG4gICAgICAgIGlkLFxuICAgICAgICBpbml0aWFsLFxuICAgICAgICBpc1ByZXNlbnQsXG4gICAgICAgIGN1c3RvbSxcbiAgICAgICAgb25FeGl0Q29tcGxldGU6IChjaGlsZElkKSA9PiB7XG4gICAgICAgICAgICBwcmVzZW5jZUNoaWxkcmVuLnNldChjaGlsZElkLCB0cnVlKTtcbiAgICAgICAgICAgIGZvciAoY29uc3QgaXNDb21wbGV0ZSBvZiBwcmVzZW5jZUNoaWxkcmVuLnZhbHVlcygpKSB7XG4gICAgICAgICAgICAgICAgaWYgKCFpc0NvbXBsZXRlKVxuICAgICAgICAgICAgICAgICAgICByZXR1cm47IC8vIGNhbiBzdG9wIHNlYXJjaGluZyB3aGVuIGFueSBpcyBpbmNvbXBsZXRlXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBvbkV4aXRDb21wbGV0ZSAmJiBvbkV4aXRDb21wbGV0ZSgpO1xuICAgICAgICB9LFxuICAgICAgICByZWdpc3RlcjogKGNoaWxkSWQpID0+IHtcbiAgICAgICAgICAgIHByZXNlbmNlQ2hpbGRyZW4uc2V0KGNoaWxkSWQsIGZhbHNlKTtcbiAgICAgICAgICAgIHJldHVybiAoKSA9PiBwcmVzZW5jZUNoaWxkcmVuLmRlbGV0ZShjaGlsZElkKTtcbiAgICAgICAgfSxcbiAgICB9KSwgXG4gICAgLyoqXG4gICAgICogSWYgdGhlIHByZXNlbmNlIG9mIGEgY2hpbGQgYWZmZWN0cyB0aGUgbGF5b3V0IG9mIHRoZSBjb21wb25lbnRzIGFyb3VuZCBpdCxcbiAgICAgKiB3ZSB3YW50IHRvIG1ha2UgYSBuZXcgY29udGV4dCB2YWx1ZSB0byBlbnN1cmUgdGhleSBnZXQgcmUtcmVuZGVyZWRcbiAgICAgKiBzbyB0aGV5IGNhbiBkZXRlY3QgdGhhdCBsYXlvdXQgY2hhbmdlLlxuICAgICAqL1xuICAgIHByZXNlbmNlQWZmZWN0c0xheW91dCA/IHVuZGVmaW5lZCA6IFtpc1ByZXNlbnRdKTtcbiAgICB1c2VNZW1vKCgpID0+IHtcbiAgICAgICAgcHJlc2VuY2VDaGlsZHJlbi5mb3JFYWNoKChfLCBrZXkpID0+IHByZXNlbmNlQ2hpbGRyZW4uc2V0KGtleSwgZmFsc2UpKTtcbiAgICB9LCBbaXNQcmVzZW50XSk7XG4gICAgLyoqXG4gICAgICogSWYgdGhlcmUncyBubyBgbW90aW9uYCBjb21wb25lbnRzIHRvIGZpcmUgZXhpdCBhbmltYXRpb25zLCB3ZSB3YW50IHRvIHJlbW92ZSB0aGlzXG4gICAgICogY29tcG9uZW50IGltbWVkaWF0ZWx5LlxuICAgICAqL1xuICAgIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgICFpc1ByZXNlbnQgJiZcbiAgICAgICAgICAgICFwcmVzZW5jZUNoaWxkcmVuLnNpemUgJiZcbiAgICAgICAgICAgIG9uRXhpdENvbXBsZXRlICYmXG4gICAgICAgICAgICBvbkV4aXRDb21wbGV0ZSgpO1xuICAgIH0sIFtpc1ByZXNlbnRdKTtcbiAgICBpZiAobW9kZSA9PT0gXCJwb3BMYXlvdXRcIikge1xuICAgICAgICBjaGlsZHJlbiA9IFJlYWN0LmNyZWF0ZUVsZW1lbnQoUG9wQ2hpbGQsIHsgaXNQcmVzZW50OiBpc1ByZXNlbnQgfSwgY2hpbGRyZW4pO1xuICAgIH1cbiAgICByZXR1cm4gKFJlYWN0LmNyZWF0ZUVsZW1lbnQoUHJlc2VuY2VDb250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBjb250ZXh0IH0sIGNoaWxkcmVuKSk7XG59O1xuZnVuY3Rpb24gbmV3Q2hpbGRyZW5NYXAoKSB7XG4gICAgcmV0dXJuIG5ldyBNYXAoKTtcbn1cblxuZXhwb3J0IHsgUHJlc2VuY2VDaGlsZCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs":
/*!*********************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnimatePresence: function() { return /* binding */ AnimatePresence; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _utils_use_force_update_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/use-force-update.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-force-update.mjs\");\n/* harmony import */ var _utils_use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-is-mounted.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs\");\n/* harmony import */ var _PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PresenceChild.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/PresenceChild.mjs\");\n/* harmony import */ var _context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../context/LayoutGroupContext.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/context/LayoutGroupContext.mjs\");\n/* harmony import */ var _utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../utils/use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n/* harmony import */ var _utils_use_unmount_effect_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../utils/use-unmount-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs\");\n/* harmony import */ var _utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils/errors.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/errors.mjs\");\n\n\n\n\n\n\n\n\n\n\nconst getChildKey = (child) => child.key || \"\";\nfunction updateChildLookup(children, allChildren) {\n    children.forEach((child) => {\n        const key = getChildKey(child);\n        allChildren.set(key, child);\n    });\n}\nfunction onlyElements(children) {\n    const filtered = [];\n    // We use forEach here instead of map as map mutates the component key by preprending `.$`\n    react__WEBPACK_IMPORTED_MODULE_0__.Children.forEach(children, (child) => {\n        if ((0,react__WEBPACK_IMPORTED_MODULE_0__.isValidElement)(child))\n            filtered.push(child);\n    });\n    return filtered;\n}\n/**\n * `AnimatePresence` enables the animation of components that have been removed from the tree.\n *\n * When adding/removing more than a single child, every child **must** be given a unique `key` prop.\n *\n * Any `motion` components that have an `exit` property defined will animate out when removed from\n * the tree.\n *\n * ```jsx\n * import { motion, AnimatePresence } from 'framer-motion'\n *\n * export const Items = ({ items }) => (\n *   <AnimatePresence>\n *     {items.map(item => (\n *       <motion.div\n *         key={item.id}\n *         initial={{ opacity: 0 }}\n *         animate={{ opacity: 1 }}\n *         exit={{ opacity: 0 }}\n *       />\n *     ))}\n *   </AnimatePresence>\n * )\n * ```\n *\n * You can sequence exit animations throughout a tree using variants.\n *\n * If a child contains multiple `motion` components with `exit` props, it will only unmount the child\n * once all `motion` components have finished animating out. Likewise, any components using\n * `usePresence` all need to call `safeToRemove`.\n *\n * @public\n */\nconst AnimatePresence = ({ children, custom, initial = true, onExitComplete, exitBeforeEnter, presenceAffectsLayout = true, mode = \"sync\", }) => {\n    (0,_utils_errors_mjs__WEBPACK_IMPORTED_MODULE_1__.invariant)(!exitBeforeEnter, \"Replace exitBeforeEnter with mode='wait'\");\n    // We want to force a re-render once all exiting animations have finished. We\n    // either use a local forceRender function, or one from a parent context if it exists.\n    const forceRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_context_LayoutGroupContext_mjs__WEBPACK_IMPORTED_MODULE_2__.LayoutGroupContext).forceRender || (0,_utils_use_force_update_mjs__WEBPACK_IMPORTED_MODULE_3__.useForceUpdate)()[0];\n    const isMounted = (0,_utils_use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_4__.useIsMounted)();\n    // Filter out any children that aren't ReactElements. We can only track ReactElements with a props.key\n    const filteredChildren = onlyElements(children);\n    let childrenToRender = filteredChildren;\n    const exitingChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map()).current;\n    // Keep a living record of the children we're actually rendering so we\n    // can diff to figure out which are entering and exiting\n    const presentChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(childrenToRender);\n    // A lookup table to quickly reference components by key\n    const allChildren = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map()).current;\n    // If this is the initial component render, just deal with logic surrounding whether\n    // we play onMount animations or not.\n    const isInitialRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    (0,_utils_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_5__.useIsomorphicLayoutEffect)(() => {\n        isInitialRender.current = false;\n        updateChildLookup(filteredChildren, allChildren);\n        presentChildren.current = childrenToRender;\n    });\n    (0,_utils_use_unmount_effect_mjs__WEBPACK_IMPORTED_MODULE_6__.useUnmountEffect)(() => {\n        isInitialRender.current = true;\n        allChildren.clear();\n        exitingChildren.clear();\n    });\n    if (isInitialRender.current) {\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, childrenToRender.map((child) => (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: true, initial: initial ? undefined : false, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child)))));\n    }\n    // If this is a subsequent render, deal with entering and exiting children\n    childrenToRender = [...childrenToRender];\n    // Diff the keys of the currently-present and target children to update our\n    // exiting list.\n    const presentKeys = presentChildren.current.map(getChildKey);\n    const targetKeys = filteredChildren.map(getChildKey);\n    // Diff the present children with our target children and mark those that are exiting\n    const numPresent = presentKeys.length;\n    for (let i = 0; i < numPresent; i++) {\n        const key = presentKeys[i];\n        if (targetKeys.indexOf(key) === -1 && !exitingChildren.has(key)) {\n            exitingChildren.set(key, undefined);\n        }\n    }\n    // If we currently have exiting children, and we're deferring rendering incoming children\n    // until after all current children have exiting, empty the childrenToRender array\n    if (mode === \"wait\" && exitingChildren.size) {\n        childrenToRender = [];\n    }\n    // Loop through all currently exiting components and clone them to overwrite `animate`\n    // with any `exit` prop they might have defined.\n    exitingChildren.forEach((component, key) => {\n        // If this component is actually entering again, early return\n        if (targetKeys.indexOf(key) !== -1)\n            return;\n        const child = allChildren.get(key);\n        if (!child)\n            return;\n        const insertionIndex = presentKeys.indexOf(key);\n        let exitingComponent = component;\n        if (!exitingComponent) {\n            const onExit = () => {\n                // clean up the exiting children map\n                exitingChildren.delete(key);\n                // compute the keys of children that were rendered once but are no longer present\n                // this could happen in case of too many fast consequent renderings\n                // @link https://github.com/framer/motion/issues/2023\n                const leftOverKeys = Array.from(allChildren.keys()).filter((childKey) => !targetKeys.includes(childKey));\n                // clean up the all children map\n                leftOverKeys.forEach((leftOverKey) => allChildren.delete(leftOverKey));\n                // make sure to render only the children that are actually visible\n                presentChildren.current = filteredChildren.filter((presentChild) => {\n                    const presentChildKey = getChildKey(presentChild);\n                    return (\n                    // filter out the node exiting\n                    presentChildKey === key ||\n                        // filter out the leftover children\n                        leftOverKeys.includes(presentChildKey));\n                });\n                // Defer re-rendering until all exiting children have indeed left\n                if (!exitingChildren.size) {\n                    if (isMounted.current === false)\n                        return;\n                    forceRender();\n                    onExitComplete && onExitComplete();\n                }\n            };\n            exitingComponent = (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: false, onExitComplete: onExit, custom: custom, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n            exitingChildren.set(key, exitingComponent);\n        }\n        childrenToRender.splice(insertionIndex, 0, exitingComponent);\n    });\n    // Add `MotionContext` even to children that don't need it to ensure we're rendering\n    // the same tree between renders\n    childrenToRender = childrenToRender.map((child) => {\n        const key = child.key;\n        return exitingChildren.has(key) ? (child) : (react__WEBPACK_IMPORTED_MODULE_0__.createElement(_PresenceChild_mjs__WEBPACK_IMPORTED_MODULE_7__.PresenceChild, { key: getChildKey(child), isPresent: true, presenceAffectsLayout: presenceAffectsLayout, mode: mode }, child));\n    });\n    if ( true &&\n        mode === \"wait\" &&\n        childrenToRender.length > 1) {\n        console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to \"wait\". This will lead to odd visual behaviour.`);\n    }\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, exitingChildren.size\n        ? childrenToRender\n        : childrenToRender.map((child) => (0,react__WEBPACK_IMPORTED_MODULE_0__.cloneElement)(child))));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-force-update.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-force-update.mjs ***!
  \***********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useForceUpdate: function() { return /* binding */ useForceUpdate; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-is-mounted.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs\");\n/* harmony import */ var _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../frameloop/frame.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/frameloop/frame.mjs\");\n\n\n\n\nfunction useForceUpdate() {\n    const isMounted = (0,_use_is_mounted_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsMounted)();\n    const [forcedRenderCount, setForcedRenderCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const forceRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        isMounted.current && setForcedRenderCount(forcedRenderCount + 1);\n    }, [forcedRenderCount]);\n    /**\n     * Defer this to the end of the next animation frame in case there are multiple\n     * synchronous calls.\n     */\n    const deferredForceRender = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => _frameloop_frame_mjs__WEBPACK_IMPORTED_MODULE_2__.frame.postRender(forceRender), [forceRender]);\n    return [deferredForceRender, forcedRenderCount];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWZvcmNlLXVwZGF0ZS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUE4QztBQUNNO0FBQ0w7O0FBRS9DO0FBQ0Esc0JBQXNCLGlFQUFZO0FBQ2xDLHNEQUFzRCwrQ0FBUTtBQUM5RCx3QkFBd0Isa0RBQVc7QUFDbkM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msa0RBQVcsT0FBTyx1REFBSztBQUN2RDtBQUNBOztBQUUwQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3VzZS1mb3JjZS11cGRhdGUubWpzPzczNGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlSXNNb3VudGVkIH0gZnJvbSAnLi91c2UtaXMtbW91bnRlZC5tanMnO1xuaW1wb3J0IHsgZnJhbWUgfSBmcm9tICcuLi9mcmFtZWxvb3AvZnJhbWUubWpzJztcblxuZnVuY3Rpb24gdXNlRm9yY2VVcGRhdGUoKSB7XG4gICAgY29uc3QgaXNNb3VudGVkID0gdXNlSXNNb3VudGVkKCk7XG4gICAgY29uc3QgW2ZvcmNlZFJlbmRlckNvdW50LCBzZXRGb3JjZWRSZW5kZXJDb3VudF0gPSB1c2VTdGF0ZSgwKTtcbiAgICBjb25zdCBmb3JjZVJlbmRlciA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAgICAgaXNNb3VudGVkLmN1cnJlbnQgJiYgc2V0Rm9yY2VkUmVuZGVyQ291bnQoZm9yY2VkUmVuZGVyQ291bnQgKyAxKTtcbiAgICB9LCBbZm9yY2VkUmVuZGVyQ291bnRdKTtcbiAgICAvKipcbiAgICAgKiBEZWZlciB0aGlzIHRvIHRoZSBlbmQgb2YgdGhlIG5leHQgYW5pbWF0aW9uIGZyYW1lIGluIGNhc2UgdGhlcmUgYXJlIG11bHRpcGxlXG4gICAgICogc3luY2hyb25vdXMgY2FsbHMuXG4gICAgICovXG4gICAgY29uc3QgZGVmZXJyZWRGb3JjZVJlbmRlciA9IHVzZUNhbGxiYWNrKCgpID0+IGZyYW1lLnBvc3RSZW5kZXIoZm9yY2VSZW5kZXIpLCBbZm9yY2VSZW5kZXJdKTtcbiAgICByZXR1cm4gW2RlZmVycmVkRm9yY2VSZW5kZXIsIGZvcmNlZFJlbmRlckNvdW50XTtcbn1cblxuZXhwb3J0IHsgdXNlRm9yY2VVcGRhdGUgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-force-update.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs ***!
  \*********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useIsMounted: function() { return /* binding */ useIsMounted; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./use-isomorphic-effect.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-isomorphic-effect.mjs\");\n\n\n\nfunction useIsMounted() {\n    const isMounted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,_use_isomorphic_effect_mjs__WEBPACK_IMPORTED_MODULE_1__.useIsomorphicLayoutEffect)(() => {\n        isMounted.current = true;\n        return () => {\n            isMounted.current = false;\n        };\n    }, []);\n    return isMounted;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWlzLW1vdW50ZWQubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQjtBQUN5Qzs7QUFFeEU7QUFDQSxzQkFBc0IsNkNBQU07QUFDNUIsSUFBSSxxRkFBeUI7QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFd0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2ZyYW1lci1tb3Rpb24vZGlzdC9lcy91dGlscy91c2UtaXMtbW91bnRlZC5tanM/NTljNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0IH0gZnJvbSAnLi91c2UtaXNvbW9ycGhpYy1lZmZlY3QubWpzJztcblxuZnVuY3Rpb24gdXNlSXNNb3VudGVkKCkge1xuICAgIGNvbnN0IGlzTW91bnRlZCA9IHVzZVJlZihmYWxzZSk7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlzTW91bnRlZC5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIGlzTW91bnRlZC5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgIH07XG4gICAgfSwgW10pO1xuICAgIHJldHVybiBpc01vdW50ZWQ7XG59XG5cbmV4cG9ydCB7IHVzZUlzTW91bnRlZCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-is-mounted.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUnmountEffect: function() { return /* binding */ useUnmountEffect; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n\nfunction useUnmountEffect(callback) {\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => () => callback(), []);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLXVubW91bnQtZWZmZWN0Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFrQzs7QUFFbEM7QUFDQSxXQUFXLGdEQUFTO0FBQ3BCOztBQUU0QiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvZnJhbWVyLW1vdGlvbi9kaXN0L2VzL3V0aWxzL3VzZS11bm1vdW50LWVmZmVjdC5tanM/ZGQxMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbmZ1bmN0aW9uIHVzZVVubW91bnRFZmZlY3QoY2FsbGJhY2spIHtcbiAgICByZXR1cm4gdXNlRWZmZWN0KCgpID0+ICgpID0+IGNhbGxiYWNrKCksIFtdKTtcbn1cblxuZXhwb3J0IHsgdXNlVW5tb3VudEVmZmVjdCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-unmount-effect.mjs\n"));

/***/ })

});