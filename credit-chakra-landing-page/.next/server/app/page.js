/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fapp%2Fglobals.css&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22src%2Fapp%2Flayout.tsx%22%2C%22import%22%3A%22Poppins%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22300%22%2C%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%2C%22800%22%2C%22900%22%5D%2C%22variable%22%3A%22--font-poppins%22%2C%22display%22%3A%22swap%22%7D%5D%2C%22variableName%22%3A%22poppins%22%7D&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fapp%2Fglobals.css&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FCTABanner.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHero.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHowItWorks.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FImpactMetrics.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FPlatformPreview.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FTestimonials.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FUseCasesTabs.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FValuePropositions.tsx&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FCTABanner.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHero.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHowItWorks.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FImpactMetrics.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FPlatformPreview.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FTestimonials.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FUseCasesTabs.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FValuePropositions.tsx&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/CTABanner.tsx */ \"(ssr)/./src/components/CTABanner.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Hero.tsx */ \"(ssr)/./src/components/Hero.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/HowItWorks.tsx */ \"(ssr)/./src/components/HowItWorks.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ImpactMetrics.tsx */ \"(ssr)/./src/components/ImpactMetrics.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PlatformPreview.tsx */ \"(ssr)/./src/components/PlatformPreview.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/Testimonials.tsx */ \"(ssr)/./src/components/Testimonials.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/UseCasesTabs.tsx */ \"(ssr)/./src/components/UseCasesTabs.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ValuePropositions.tsx */ \"(ssr)/./src/components/ValuePropositions.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FCTABanner.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHero.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FHowItWorks.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FImpactMetrics.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FPlatformPreview.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FTestimonials.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FUseCasesTabs.tsx&modules=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fcomponents%2FValuePropositions.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/CTABanner.tsx":
/*!**************************************!*\
  !*** ./src/components/CTABanner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CTABanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Mail,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction CTABanner() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative py-20 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"w-full h-full bg-gradient-to-br from-emerald-600 via-green-700 to-emerald-800\",\n                        animate: {\n                            backgroundPosition: [\n                                \"0% 50%\",\n                                \"100% 50%\",\n                                \"0% 50%\"\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"linear\"\n                        },\n                        style: {\n                            backgroundSize: \"200% 200%\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent\",\n                        animate: {\n                            x: [\n                                \"-100%\",\n                                \"100%\"\n                            ]\n                        },\n                        transition: {\n                            duration: 3,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-20 left-20 w-32 h-32 bg-white/10 rounded-full\",\n                        animate: {\n                            y: [\n                                0,\n                                -20,\n                                0\n                            ],\n                            rotate: [\n                                0,\n                                180,\n                                360\n                            ]\n                        },\n                        transition: {\n                            duration: 6,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute bottom-20 right-20 w-24 h-24 bg-accent-gold/20 rounded-full\",\n                        animate: {\n                            y: [\n                                0,\n                                15,\n                                0\n                            ],\n                            rotate: [\n                                0,\n                                -180,\n                                -360\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                        className: \"absolute top-1/2 left-1/4 w-16 h-16 bg-emerald-300/20 rounded-full\",\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            opacity: [\n                                0.3,\n                                0.6,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            duration: 4,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 text-center text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl lg:text-6xl font-bold mb-6 leading-tight\",\n                            children: [\n                                \"See Credit Chakra in\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-accent-gold\",\n                                    children: \"Action\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl lg:text-2xl text-emerald-100 mb-8 max-w-4xl mx-auto leading-relaxed\",\n                            children: \"Transform your MSME lending operations with AI-powered insights. Join leading financial institutions who trust Credit Chakra to drive growth and reduce risk.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"flex flex-col sm:flex-row gap-6 justify-center items-center mb-12\",\n                            initial: {\n                                opacity: 0,\n                                scale: 0.95\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.3\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    size: \"lg\",\n                                    className: \"bg-white text-emerald-600 hover:bg-emerald-50 font-semibold px-8 py-4 text-lg shadow-premium hover:shadow-premium-hover group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Request a Demo\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    size: \"lg\",\n                                    variant: \"outline\",\n                                    className: \"border-white text-white hover:bg-white hover:text-emerald-600 font-semibold px-8 py-4 text-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Talk to Sales\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"grid md:grid-cols-3 gap-8 max-w-4xl mx-auto\",\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.5\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                                lineNumber: 134,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Schedule Demo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-emerald-100 text-sm text-center\",\n                                            children: \"Book a personalized demo to see how Credit Chakra can transform your operations\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Speak with Expert\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-emerald-100 text-sm text-center\",\n                                            children: \"Connect with our solution experts to discuss your specific requirements\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Mail_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-8 h-8 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"Get Proposal\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-emerald-100 text-sm text-center\",\n                                            children: \"Receive a customized proposal tailored to your organization's needs\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"mt-16 pt-8 border-t border-white/20\",\n                            initial: {\n                                opacity: 0\n                            },\n                            whileInView: {\n                                opacity: 1\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.7\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-emerald-100 mb-6\",\n                                    children: \"Trusted by 100+ financial institutions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap justify-center items-center gap-8 opacity-60\",\n                                    children: [\n                                        \"Enterprise Security\",\n                                        \"99.9% Uptime\",\n                                        \"24/7 Support\",\n                                        \"ISO 27001\",\n                                        \"SOC 2 Compliant\"\n                                    ].map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            className: \"bg-white/10 px-4 py-2 rounded-full text-sm font-medium\",\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.8\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                scale: 1\n                                            },\n                                            transition: {\n                                                duration: 0.5,\n                                                delay: 0.9 + index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: badge\n                                        }, index, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CTABanner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Hero() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-emerald-500 via-green-600 to-emerald-800 animate-gradient\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4 py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.h1, {\n                                    className: \"text-5xl lg:text-7xl font-bold leading-tight\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        ease: \"easeOut\"\n                                    },\n                                    children: [\n                                        \"Smarter MSME Credit Intelligence for the\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-accent-gold\",\n                                            children: \"Future\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.p, {\n                                    className: \"text-xl lg:text-2xl text-emerald-100 leading-relaxed max-w-2xl\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.8,\n                                        delay: 0.2,\n                                        ease: \"easeOut\"\n                                    },\n                                    children: \"AI-powered scoring, monitoring, and early-warning systems — helping lenders and policymakers accelerate MSME growth.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"flex flex-col sm:flex-row gap-4\",\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.95\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.4,\n                                        ease: \"easeOut\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-emerald-500 hover:bg-emerald-600 text-white font-semibold px-8 py-4 text-lg shadow-premium hover:shadow-premium-hover\",\n                                            children: \"Request a Demo\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            size: \"lg\",\n                                            variant: \"secondary\",\n                                            className: \"bg-white text-emerald-600 hover:bg-emerald-50 font-semibold px-8 py-4 text-lg\",\n                                            children: \"Explore Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                            lineNumber: 51,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"relative\",\n                            initial: {\n                                opacity: 0,\n                                x: 50\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.3,\n                                ease: \"easeOut\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-effect rounded-3xl p-8 shadow-premium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-2xl p-6 shadow-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-8 w-32 bg-emerald-500 rounded-lg\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-6 bg-emerald-200 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 76,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-6 bg-emerald-300 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 77,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-6 bg-emerald-400 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 78,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                            lineNumber: 75,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-3 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-emerald-50 p-4 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 w-16 bg-emerald-200 rounded mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 85,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-12 bg-emerald-500 rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 86,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-green-50 p-4 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 w-16 bg-green-200 rounded mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 89,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-12 bg-green-500 rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 90,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-accent-gold/20 p-4 rounded-xl\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 w-16 bg-accent-gold/40 rounded mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 93,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-6 w-12 bg-accent-gold rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 94,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                    lineNumber: 83,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 rounded-xl p-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-24 bg-gray-300 rounded mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-end space-x-2 h-32\",\n                                                            children: [\n                                                                ...Array(8)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"bg-emerald-400 rounded-t flex-1\",\n                                                                    style: {\n                                                                        height: `${Math.random() * 80 + 20}%`\n                                                                    }\n                                                                }, i, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 103,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                            lineNumber: 101,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        ...Array(3)\n                                                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 w-3 bg-emerald-500 rounded-full\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 116,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 flex-1 bg-gray-200 rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 117,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 w-16 bg-emerald-300 rounded\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                                    lineNumber: 118,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"absolute -top-4 -right-4 w-16 h-16 bg-accent-gold rounded-full opacity-80\",\n                                    animate: {\n                                        y: [\n                                            0,\n                                            -10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 3,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\"\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"absolute -bottom-6 -left-6 w-12 h-12 bg-emerald-300 rounded-full opacity-60\",\n                                    animate: {\n                                        y: [\n                                            0,\n                                            10,\n                                            0\n                                        ]\n                                    },\n                                    transition: {\n                                        duration: 4,\n                                        repeat: Infinity,\n                                        ease: \"easeInOut\",\n                                        delay: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-0 left-0 right-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    viewBox: \"0 0 1440 120\",\n                    className: \"w-full h-20 fill-white\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M0,64L48,69.3C96,75,192,85,288,80C384,75,480,53,576,48C672,43,768,53,864,64C960,75,1056,85,1152,80C1248,75,1344,53,1392,42.7L1440,32L1440,120L1392,120C1344,120,1248,120,1152,120C1056,120,960,120,864,120C768,120,672,120,576,120C480,120,384,120,288,120C192,120,96,120,48,120L0,120Z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/HowItWorks.tsx":
/*!***************************************!*\
  !*** ./src/components/HowItWorks.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HowItWorks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Shield,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Shield,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Shield,UserPlus!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst steps = [\n    {\n        icon: _barrel_optimize_names_BarChart3_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Connect & Onboard\",\n        description: \"Seamlessly integrate with existing systems and onboard MSMEs through our secure API connections and data aggregation platform.\",\n        features: [\n            \"API Integration\",\n            \"Data Aggregation\",\n            \"Secure Onboarding\",\n            \"System Compatibility\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"Analyze & Score\",\n        description: \"Our AI algorithms analyze multiple data sources to generate comprehensive credit scores and risk assessments in real-time.\",\n        features: [\n            \"AI-Powered Analysis\",\n            \"Multi-Source Data\",\n            \"Real-time Scoring\",\n            \"Risk Assessment\"\n        ]\n    },\n    {\n        icon: _barrel_optimize_names_BarChart3_Shield_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Monitor & Act\",\n        description: \"Continuous monitoring with early warning systems and actionable insights to help you make informed lending decisions.\",\n        features: [\n            \"Continuous Monitoring\",\n            \"Early Warnings\",\n            \"Actionable Insights\",\n            \"Decision Support\"\n        ]\n    }\n];\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.3\n        }\n    }\n};\nconst stepVariants = {\n    hidden: {\n        opacity: 0,\n        y: 50\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.8,\n            ease: \"easeOut\"\n        }\n    }\n};\nfunction HowItWorks() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\",\n                            children: \"How Credit Chakra Works\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Our streamlined process transforms complex financial data into actionable insights through three simple steps.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"grid lg:grid-cols-3 gap-8\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: steps.map((step, index)=>{\n                        const IconComponent = step.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                            variants: stepVariants,\n                            className: \"relative\",\n                            children: [\n                                index < steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-emerald-400 to-green-600 z-10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                    className: \"h-full bg-gradient-to-tr from-emerald-400 to-green-600 text-white border-none shadow-premium hover:shadow-premium-hover transition-all duration-300 group\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        className: \"p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-6xl font-bold text-white/20\",\n                                                        children: String(index + 1).padStart(2, \"0\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                        lineNumber: 111,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold mb-4\",\n                                                children: step.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-emerald-50 mb-6 leading-relaxed\",\n                                                children: step.description\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: step.features.map((feature, featureIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                                        className: \"flex items-center space-x-3\",\n                                                        initial: {\n                                                            opacity: 0,\n                                                            x: -20\n                                                        },\n                                                        whileInView: {\n                                                            opacity: 1,\n                                                            x: 0\n                                                        },\n                                                        transition: {\n                                                            duration: 0.5,\n                                                            delay: 0.5 + index * 0.3 + featureIndex * 0.1\n                                                        },\n                                                        viewport: {\n                                                            once: true\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 bg-accent-gold rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-emerald-50 font-medium\",\n                                                                children: feature\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                                lineNumber: 137,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, featureIndex, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                    className: \"mt-20 bg-gradient-to-r from-emerald-50 to-green-50 rounded-3xl p-12\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.5\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"End-to-End Process Flow\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"From data ingestion to actionable insights in minutes, not days\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0 lg:space-x-8\",\n                            children: [\n                                \"Data Sources\",\n                                \"AI Processing\",\n                                \"Risk Analysis\",\n                                \"Insights Dashboard\",\n                                \"Decision Support\"\n                            ].map((stage, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                    className: \"flex flex-col items-center\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.6,\n                                        delay: 0.8 + index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-emerald-500 rounded-2xl flex items-center justify-center mb-3 shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-white rounded-lg\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700 text-center\",\n                                            children: stage\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        index < 4 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"hidden lg:block w-8 h-0.5 bg-emerald-300 mt-8 absolute translate-x-12\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/HowItWorks.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ImpactMetrics.tsx":
/*!******************************************!*\
  !*** ./src/components/ImpactMetrics.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImpactMetrics)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction AnimatedCounter({ value, suffix, label, description, duration = 2 }) {\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isInView) {\n            let startTime;\n            let animationFrame;\n            const animate = (timestamp)=>{\n                if (!startTime) startTime = timestamp;\n                const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);\n                setCount(Math.floor(progress * value));\n                if (progress < 1) {\n                    animationFrame = requestAnimationFrame(animate);\n                }\n            };\n            animationFrame = requestAnimationFrame(animate);\n            return ()=>cancelAnimationFrame(animationFrame);\n        }\n    }, [\n        isInView,\n        value,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n        ref: ref,\n        className: \"text-center\",\n        initial: {\n            opacity: 0,\n            y: 30\n        },\n        whileInView: {\n            opacity: 1,\n            y: 0\n        },\n        transition: {\n            duration: 0.8\n        },\n        viewport: {\n            once: true\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-6xl lg:text-7xl font-bold text-gradient mb-4\",\n                children: [\n                    count,\n                    suffix\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-xl font-semibold text-gray-900 mb-2\",\n                children: label\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-gray-600 max-w-xs mx-auto\",\n                children: description\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\nconst metrics = [\n    {\n        value: 20,\n        suffix: \"%\",\n        label: \"Faster Risk Detection\",\n        description: \"Reduce time to identify potential defaults with AI-powered early warning systems\"\n    },\n    {\n        value: 30,\n        suffix: \"%\",\n        label: \"Reduction in NPLs\",\n        description: \"Significant decrease in non-performing loans through predictive analytics\"\n    },\n    {\n        value: 50,\n        suffix: \"K+\",\n        label: \"MSMEs Profiled\",\n        description: \"Comprehensive credit profiles created for micro, small, and medium enterprises\"\n    },\n    {\n        value: 95,\n        suffix: \"%\",\n        label: \"Accuracy Rate\",\n        description: \"Industry-leading precision in credit risk assessment and scoring models\"\n    }\n];\nfunction ImpactMetrics() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-br from-emerald-900 via-green-800 to-emerald-900 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 opacity-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0\",\n                    style: {\n                        backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 max-w-7xl mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"text-center mb-16\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl lg:text-5xl font-bold mb-6\",\n                                children: \"Measurable Impact on MSME Lending\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-emerald-100 max-w-3xl mx-auto\",\n                                children: \"Our platform delivers quantifiable results that transform lending operations and drive sustainable growth for financial institutions.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-12\",\n                        children: metrics.map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                value: metric.value,\n                                suffix: metric.suffix,\n                                label: metric.label,\n                                description: metric.description,\n                                duration: 2 + index * 0.2\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"mt-20 grid md:grid-cols-3 gap-8\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.5\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            {\n                                title: \"Processing Speed\",\n                                description: \"Credit assessments completed in minutes instead of days\",\n                                highlight: \"10x Faster\"\n                            },\n                            {\n                                title: \"Data Integration\",\n                                description: \"Seamless connection with multiple financial data sources\",\n                                highlight: \"50+ APIs\"\n                            },\n                            {\n                                title: \"Cost Reduction\",\n                                description: \"Lower operational costs through automated risk assessment\",\n                                highlight: \"40% Savings\"\n                            }\n                        ].map((story, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                className: \"text-center p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20\",\n                                initial: {\n                                    opacity: 0,\n                                    scale: 0.95\n                                },\n                                whileInView: {\n                                    opacity: 1,\n                                    scale: 1\n                                },\n                                transition: {\n                                    duration: 0.6,\n                                    delay: 0.7 + index * 0.1\n                                },\n                                viewport: {\n                                    once: true\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-3xl font-bold text-accent-gold mb-3\",\n                                        children: story.highlight\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: story.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-emerald-100 text-sm\",\n                                        children: story.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                        className: \"mt-16 text-center\",\n                        initial: {\n                            opacity: 0,\n                            y: 30\n                        },\n                        whileInView: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            duration: 0.8,\n                            delay: 0.8\n                        },\n                        viewport: {\n                            once: true\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold mb-4\",\n                                children: \"Ready to Transform Your MSME Lending?\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-emerald-100 mb-8 max-w-2xl mx-auto\",\n                                children: \"Join leading financial institutions who trust Credit Chakra to drive their digital transformation and improve lending outcomes.\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.button, {\n                                className: \"bg-accent-gold text-gray-900 px-8 py-4 rounded-2xl font-semibold text-lg hover:bg-yellow-400 transition-colors duration-300 shadow-lg hover:shadow-xl\",\n                                whileHover: {\n                                    scale: 1.05\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                children: \"Schedule a Demo\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ImpactMetrics.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/PlatformPreview.tsx":
/*!********************************************!*\
  !*** ./src/components/PlatformPreview.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PlatformPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction PlatformPreview() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-br from-emerald-50 to-green-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\",\n                            children: \"See Credit Chakra in Action\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Experience our intuitive dashboard that transforms complex financial data into actionable insights for smarter lending decisions.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"mb-16\",\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        scale: 1\n                    },\n                    transition: {\n                        duration: 1,\n                        ease: \"easeOut\"\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                        className: \"p-8 shadow-premium\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl p-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-emerald-500 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-white rounded-md\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                        lineNumber: 40,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 39,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-6 w-32 bg-gray-300 rounded mb-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                            lineNumber: 43,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 w-24 bg-gray-200 rounded\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                            lineNumber: 44,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 42,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 w-24 bg-emerald-500 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 w-10 bg-gray-200 rounded-xl\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 49,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                                    children: [\n                                        {\n                                            color: \"bg-emerald-500\",\n                                            label: \"Portfolio Health\"\n                                        },\n                                        {\n                                            color: \"bg-green-500\",\n                                            label: \"Active MSMEs\"\n                                        },\n                                        {\n                                            color: \"bg-amber-500\",\n                                            label: \"Risk Alerts\"\n                                        },\n                                        {\n                                            color: \"bg-blue-500\",\n                                            label: \"Compliance Score\"\n                                        }\n                                    ].map((metric, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                            className: \"bg-white rounded-xl p-6 shadow-lg\",\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            whileInView: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                duration: 0.6,\n                                                delay: index * 0.1\n                                            },\n                                            viewport: {\n                                                once: true\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-12 h-12 ${metric.color} rounded-lg mb-4`\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-8 w-16 bg-gray-800 rounded mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-4 w-20 bg-gray-300 rounded\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid lg:grid-cols-3 gap-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"lg:col-span-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-xl p-6 shadow-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"h-6 w-32 bg-gray-300 rounded\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                lineNumber: 81,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-8 w-16 bg-emerald-100 rounded-lg\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                        lineNumber: 83,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-8 w-16 bg-gray-100 rounded-lg\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                        lineNumber: 84,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                lineNumber: 82,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                        lineNumber: 80,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-64 bg-gradient-to-t from-emerald-50 to-transparent rounded-lg flex items-end justify-between px-4 pb-4\",\n                                                        children: [\n                                                            ...Array(12)\n                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                className: \"bg-emerald-400 rounded-t w-8\",\n                                                                style: {\n                                                                    height: `${Math.random() * 80 + 20}%`\n                                                                },\n                                                                initial: {\n                                                                    height: 0\n                                                                },\n                                                                whileInView: {\n                                                                    height: `${Math.random() * 80 + 20}%`\n                                                                },\n                                                                transition: {\n                                                                    duration: 1,\n                                                                    delay: i * 0.1\n                                                                },\n                                                                viewport: {\n                                                                    once: true\n                                                                }\n                                                            }, i, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                lineNumber: 89,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                        lineNumber: 87,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-xl p-6 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-5 w-24 bg-gray-300 rounded mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                {\n                                                                    color: \"bg-green-500\",\n                                                                    width: \"w-3/4\"\n                                                                },\n                                                                {\n                                                                    color: \"bg-amber-500\",\n                                                                    width: \"w-1/2\"\n                                                                },\n                                                                {\n                                                                    color: \"bg-red-500\",\n                                                                    width: \"w-1/4\"\n                                                                }\n                                                            ].map((bar, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: `w-3 h-3 ${bar.color} rounded-full`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                            lineNumber: 114,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1 bg-gray-100 rounded-full h-2\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                                className: `h-2 ${bar.color} rounded-full ${bar.width}`,\n                                                                                initial: {\n                                                                                    width: 0\n                                                                                },\n                                                                                whileInView: {\n                                                                                    width: bar.width\n                                                                                },\n                                                                                transition: {\n                                                                                    duration: 1,\n                                                                                    delay: 0.5 + index * 0.2\n                                                                                },\n                                                                                viewport: {\n                                                                                    once: true\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                                lineNumber: 116,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                            lineNumber: 115,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                    lineNumber: 113,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-xl p-6 shadow-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-5 w-28 bg-gray-300 rounded mb-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-3\",\n                                                            children: [\n                                                                ...Array(4)\n                                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        x: 20\n                                                                    },\n                                                                    whileInView: {\n                                                                        opacity: 1,\n                                                                        x: 0\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.5,\n                                                                        delay: 0.8 + i * 0.1\n                                                                    },\n                                                                    viewport: {\n                                                                        once: true\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-emerald-100 rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                            lineNumber: 142,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-3 w-full bg-gray-200 rounded mb-1\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                                    lineNumber: 144,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"h-2 w-2/3 bg-gray-100 rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                                    lineNumber: 145,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                            lineNumber: 143,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, i, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"grid md:grid-cols-3 gap-8\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.3\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        \"Real-time Risk Monitoring\",\n                        \"Predictive Analytics\",\n                        \"Compliance Tracking\"\n                    ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-16 h-16 bg-emerald-500 rounded-2xl flex items-center justify-center mx-auto mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-white rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                    children: feature\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Advanced capabilities that drive better outcomes\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/PlatformPreview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Testimonials.tsx":
/*!*****************************************!*\
  !*** ./src/components/Testimonials.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Testimonials)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Quote,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/quote.js\");\n/* harmony import */ var _barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Quote,Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst testimonials = [\n    {\n        name: \"Rajesh Kumar\",\n        title: \"Chief Risk Officer\",\n        company: \"National Development Bank\",\n        content: \"Credit Chakra has revolutionized our MSME lending process. The AI-powered risk assessment has reduced our NPLs by 35% while increasing our loan approval speed by 60%.\",\n        rating: 5,\n        avatar: \"RK\"\n    },\n    {\n        name: \"Priya Sharma\",\n        title: \"Director of Financial Inclusion\",\n        company: \"Ministry of MSME\",\n        content: \"The platform provides invaluable insights into MSME ecosystem health. We can now track policy impact in real-time and make data-driven decisions for better outcomes.\",\n        rating: 5,\n        avatar: \"PS\"\n    },\n    {\n        name: \"Amit Patel\",\n        title: \"CTO\",\n        company: \"FinTech Solutions Ltd\",\n        content: \"Integration was seamless and the API response times are exceptional. Credit Chakra's white-label solution helped us launch our lending product 3 months ahead of schedule.\",\n        rating: 5,\n        avatar: \"AP\"\n    }\n];\nconst partners = [\n    {\n        name: \"Reserve Bank of India\",\n        logo: \"RBI\"\n    },\n    {\n        name: \"State Bank of India\",\n        logo: \"SBI\"\n    },\n    {\n        name: \"HDFC Bank\",\n        logo: \"HDFC\"\n    },\n    {\n        name: \"ICICI Bank\",\n        logo: \"ICICI\"\n    },\n    {\n        name: \"Axis Bank\",\n        logo: \"AXIS\"\n    },\n    {\n        name: \"Ministry of MSME\",\n        logo: \"MSME\"\n    },\n    {\n        name: \"SIDBI\",\n        logo: \"SIDBI\"\n    },\n    {\n        name: \"MUDRA\",\n        logo: \"MUDRA\"\n    }\n];\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst itemVariants = {\n    hidden: {\n        opacity: 0,\n        y: 30\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        }\n    }\n};\nfunction Testimonials() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-gradient-to-br from-gray-50 to-emerald-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\",\n                            children: \"Trusted by Industry Leaders\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"See what our partners and clients say about their experience with Credit Chakra's innovative MSME credit intelligence platform.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"grid md:grid-cols-3 gap-8 mb-20\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            variants: itemVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"h-full border-emerald-100 hover:border-emerald-300 hover:shadow-premium transition-all duration-300 group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                    className: \"p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-8 h-8 text-emerald-500 group-hover:text-emerald-600 transition-colors\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1 mb-4\",\n                                            children: [\n                                                ...Array(testimonial.rating)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Quote_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5 fill-accent-gold text-accent-gold\"\n                                                }, i, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 leading-relaxed mb-6 italic\",\n                                            children: [\n                                                '\"',\n                                                testimonial.content,\n                                                '\"'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-emerald-500 rounded-full flex items-center justify-center text-white font-semibold\",\n                                                    children: testimonial.avatar\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-semibold text-gray-900\",\n                                                            children: testimonial.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                                            lineNumber: 122,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: testimonial.title\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                                            lineNumber: 123,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-emerald-600 font-medium\",\n                                                            children: testimonial.company\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.3\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-gray-900 mb-4\",\n                                    children: \"Partnering with Leading Institutions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Trusted by banks, NBFCs, government agencies, and fintech companies across India\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-8 items-center\",\n                            children: partners.map((partner, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                                    className: \"flex items-center justify-center\",\n                                    initial: {\n                                        opacity: 0,\n                                        scale: 0.8\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        scale: 1\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: index * 0.1\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-20 h-20 bg-white rounded-2xl shadow-lg flex items-center justify-center hover:shadow-xl hover:scale-105 transition-all duration-300 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-bold text-emerald-600 group-hover:text-emerald-700\",\n                                                children: partner.logo\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    className: \"mt-16 grid md:grid-cols-4 gap-8 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.5\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        {\n                            label: \"Enterprise Clients\",\n                            value: \"100+\"\n                        },\n                        {\n                            label: \"Data Security\",\n                            value: \"ISO 27001\"\n                        },\n                        {\n                            label: \"Uptime SLA\",\n                            value: \"99.9%\"\n                        },\n                        {\n                            label: \"Support\",\n                            value: \"24/7\"\n                        }\n                    ].map((indicator, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            className: \"space-y-2\",\n                            initial: {\n                                opacity: 0,\n                                y: 20\n                            },\n                            whileInView: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.6,\n                                delay: 0.7 + index * 0.1\n                            },\n                            viewport: {\n                                once: true\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-gradient\",\n                                    children: indicator.value\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600 font-medium\",\n                                    children: indicator.label\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n            lineNumber: 70,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Testimonials.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/UseCasesTabs.tsx":
/*!*****************************************!*\
  !*** ./src/components/UseCasesTabs.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ UseCasesTabs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/tabs */ \"(ssr)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Landmark_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Landmark,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Landmark_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Landmark,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/landmark.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Landmark_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Landmark,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/smartphone.js\");\n/* harmony import */ var _barrel_optimize_names_Building2_CheckCircle_Landmark_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building2,CheckCircle,Landmark,Smartphone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst useCases = {\n    banks: {\n        title: \"Banks & NBFCs\",\n        icon: _barrel_optimize_names_Building2_CheckCircle_Landmark_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        description: \"Enhance your lending portfolio with AI-driven risk assessment and real-time monitoring capabilities.\",\n        benefits: [\n            \"Automated credit scoring for faster loan approvals\",\n            \"Real-time portfolio monitoring and risk alerts\",\n            \"Regulatory compliance and reporting automation\",\n            \"Reduced NPLs through predictive analytics\",\n            \"Enhanced customer onboarding experience\",\n            \"Integration with existing core banking systems\"\n        ],\n        stats: [\n            {\n                label: \"Faster Processing\",\n                value: \"75%\"\n            },\n            {\n                label: \"Risk Reduction\",\n                value: \"40%\"\n            },\n            {\n                label: \"Cost Savings\",\n                value: \"60%\"\n            }\n        ]\n    },\n    government: {\n        title: \"Government Agencies\",\n        icon: _barrel_optimize_names_Building2_CheckCircle_Landmark_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        description: \"Support policy implementation and MSME development with comprehensive data insights and monitoring tools.\",\n        benefits: [\n            \"Policy impact measurement and analysis\",\n            \"MSME ecosystem health monitoring\",\n            \"Subsidy and scheme effectiveness tracking\",\n            \"Regional economic development insights\",\n            \"Compliance monitoring and enforcement\",\n            \"Data-driven policy recommendations\"\n        ],\n        stats: [\n            {\n                label: \"Policy Reach\",\n                value: \"85%\"\n            },\n            {\n                label: \"Monitoring Efficiency\",\n                value: \"70%\"\n            },\n            {\n                label: \"Data Accuracy\",\n                value: \"95%\"\n            }\n        ]\n    },\n    fintechs: {\n        title: \"Fintechs\",\n        icon: _barrel_optimize_names_Building2_CheckCircle_Landmark_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        description: \"Power your fintech solutions with advanced credit intelligence and seamless API integrations.\",\n        benefits: [\n            \"White-label credit scoring solutions\",\n            \"API-first architecture for easy integration\",\n            \"Scalable infrastructure for rapid growth\",\n            \"Advanced analytics and machine learning\",\n            \"Custom risk models and parameters\",\n            \"Real-time decision engines\"\n        ],\n        stats: [\n            {\n                label: \"API Response Time\",\n                value: \"<100ms\"\n            },\n            {\n                label: \"Scalability\",\n                value: \"99.9%\"\n            },\n            {\n                label: \"Integration Speed\",\n                value: \"2 weeks\"\n            }\n        ]\n    }\n};\nfunction UseCasesTabs() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\",\n                            children: \"Tailored Solutions for Every Stakeholder\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Credit Chakra adapts to your specific needs, whether you're a financial institution, government agency, or fintech innovator.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.2\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_1__.Tabs, {\n                        defaultValue: \"banks\",\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_1__.TabsList, {\n                                className: \"grid w-full grid-cols-3 mb-12 h-16\",\n                                children: Object.entries(useCases).map(([key, useCase])=>{\n                                    const IconComponent = useCase.icon;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_1__.TabsTrigger, {\n                                        value: key,\n                                        className: \"flex items-center space-x-2 text-base font-medium h-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: useCase.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, key, true, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 13\n                            }, this),\n                            Object.entries(useCases).map(([key, useCase])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_1__.TabsContent, {\n                                    value: key,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.6\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid lg:grid-cols-2 gap-12 items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-4 mb-6\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-16 h-16 bg-emerald-500 rounded-2xl flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(useCase.icon, {\n                                                                        className: \"w-8 h-8 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                        lineNumber: 120,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                    lineNumber: 119,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                                    children: useCase.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg text-gray-600 mb-8 leading-relaxed\",\n                                                            children: useCase.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                                                    children: \"Key Benefits:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                useCase.benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                                        className: \"flex items-start space-x-3\",\n                                                                        initial: {\n                                                                            opacity: 0,\n                                                                            x: -20\n                                                                        },\n                                                                        animate: {\n                                                                            opacity: 1,\n                                                                            x: 0\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.5,\n                                                                            delay: index * 0.1\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building2_CheckCircle_Landmark_Smartphone_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                className: \"w-6 h-6 text-emerald-500 mt-0.5 flex-shrink-0\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                lineNumber: 143,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-700\",\n                                                                                children: benefit\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                lineNumber: 144,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                        lineNumber: 136,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 gap-6\",\n                                                            children: useCase.stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                                    initial: {\n                                                                        opacity: 0,\n                                                                        scale: 0.95\n                                                                    },\n                                                                    animate: {\n                                                                        opacity: 1,\n                                                                        scale: 1\n                                                                    },\n                                                                    transition: {\n                                                                        duration: 0.5,\n                                                                        delay: 0.3 + index * 0.1\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                                        className: \"p-6 border-emerald-100 hover:border-emerald-300 transition-colors\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                                            className: \"p-0\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-between\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-600 font-medium\",\n                                                                                        children: stat.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                        lineNumber: 164,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-3xl font-bold text-gradient\",\n                                                                                        children: stat.value\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                        lineNumber: 167,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                lineNumber: 163,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                            lineNumber: 162,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, index, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                    lineNumber: 155,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                                            className: \"bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl p-8\",\n                                                            initial: {\n                                                                opacity: 0,\n                                                                scale: 0.95\n                                                            },\n                                                            animate: {\n                                                                opacity: 1,\n                                                                scale: 1\n                                                            },\n                                                            transition: {\n                                                                duration: 0.6,\n                                                                delay: 0.5\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-6 w-32 bg-emerald-300 rounded\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                lineNumber: 186,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-6 w-6 bg-emerald-500 rounded-full\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                lineNumber: 187,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                        lineNumber: 185,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"grid grid-cols-2 gap-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-20 bg-emerald-200 rounded-xl\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                lineNumber: 190,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"h-20 bg-green-200 rounded-xl\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                lineNumber: 191,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                        lineNumber: 189,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            ...Array(3)\n                                                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"h-3 w-3 bg-emerald-400 rounded-full\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                        lineNumber: 196,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"h-3 flex-1 bg-emerald-100 rounded\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                        lineNumber: 197,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, i, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                                lineNumber: 195,\n                                                                                columnNumber: 31\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                        lineNumber: 193,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                                lineNumber: 184,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                            lineNumber: 178,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this)\n                                }, key, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/UseCasesTabs.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ValuePropositions.tsx":
/*!**********************************************!*\
  !*** ./src/components/ValuePropositions.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ValuePropositions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_Brain_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,Brain,Target!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst valueProps = [\n    {\n        icon: _barrel_optimize_names_Activity_AlertTriangle_Brain_Target_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        title: \"Real-Time Credit Monitoring\",\n        description: \"Continuous tracking of MSME financial health with instant alerts on risk changes and portfolio performance metrics.\",\n        color: \"text-emerald-600\",\n        bgColor: \"bg-emerald-50\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_AlertTriangle_Brain_Target_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        title: \"AI-Driven Scoring Models\",\n        description: \"Advanced machine learning algorithms that analyze multiple data sources for accurate credit risk assessment.\",\n        color: \"text-green-600\",\n        bgColor: \"bg-green-50\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_AlertTriangle_Brain_Target_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        title: \"Early-Warning Insights\",\n        description: \"Predictive analytics that identify potential defaults before they happen, enabling proactive risk management.\",\n        color: \"text-amber-600\",\n        bgColor: \"bg-amber-50\"\n    },\n    {\n        icon: _barrel_optimize_names_Activity_AlertTriangle_Brain_Target_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        title: \"Policy & ESG Alignment\",\n        description: \"Comprehensive compliance tracking and ESG scoring to support sustainable lending practices and regulatory requirements.\",\n        color: \"text-blue-600\",\n        bgColor: \"bg-blue-50\"\n    }\n];\nconst containerVariants = {\n    hidden: {\n        opacity: 0\n    },\n    visible: {\n        opacity: 1,\n        transition: {\n            staggerChildren: 0.2\n        }\n    }\n};\nconst cardVariants = {\n    hidden: {\n        opacity: 0,\n        y: 30\n    },\n    visible: {\n        opacity: 1,\n        y: 0,\n        transition: {\n            duration: 0.6,\n            ease: \"easeOut\"\n        }\n    }\n};\nfunction ValuePropositions() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-white\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: \"text-center mb-16\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl lg:text-5xl font-bold text-gray-900 mb-6\",\n                            children: \"Transforming MSME Credit Intelligence\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                            children: \"Our comprehensive platform delivers the insights and tools you need to make smarter lending decisions and drive sustainable growth.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: \"grid md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    whileInView: \"visible\",\n                    viewport: {\n                        once: true\n                    },\n                    children: valueProps.map((prop, index)=>{\n                        const IconComponent = prop.icon;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            variants: cardVariants,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                className: \"h-full border-emerald-100 hover:border-emerald-300 transition-all duration-300 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                                        className: \"text-center pb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-16 h-16 ${prop.bgColor} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: `w-8 h-8 ${prop.color}`\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                                className: \"text-xl font-semibold text-gray-900\",\n                                                children: prop.title\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                                        className: \"text-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 leading-relaxed\",\n                                            children: prop.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 17\n                            }, this)\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    className: \"mt-20 grid md:grid-cols-3 gap-8 text-center\",\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.3\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl font-bold text-gradient\",\n                                    children: \"99.9%\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600\",\n                                    children: \"System Uptime\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl font-bold text-gradient\",\n                                    children: \"50K+\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600\",\n                                    children: \"MSMEs Analyzed\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl font-bold text-gradient\",\n                                    children: \"30%\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Risk Reduction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n            lineNumber: 66,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ValuePropositions.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-2xl text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-105 duration-300\", {\n    variants: {\n        variant: {\n            default: \"bg-emerald-500 text-white hover:bg-emerald-600 shadow-lg hover:shadow-premium\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-emerald-500 bg-transparent text-emerald-600 hover:bg-emerald-50 hover:text-emerald-700\",\n            secondary: \"bg-white text-emerald-600 hover:bg-emerald-50 border border-emerald-200 shadow-lg hover:shadow-premium\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-12 px-6 py-3\",\n            sm: \"h-9 rounded-xl px-3\",\n            lg: \"h-14 rounded-2xl px-8 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-2xl border border-emerald-100 bg-white text-card-foreground shadow-lg hover:shadow-premium transition-all duration-300 hover:scale-105\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight text-gray-900\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/card.tsx\",\n        lineNumber: 32,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/card.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/card.tsx\",\n        lineNumber: 59,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/card.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/tabs.tsx":
/*!************************************!*\
  !*** ./src/components/ui/tabs.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tabs: () => (/* binding */ Tabs),\n/* harmony export */   TabsContent: () => (/* binding */ TabsContent),\n/* harmony export */   TabsList: () => (/* binding */ TabsList),\n/* harmony export */   TabsTrigger: () => (/* binding */ TabsTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tabs */ \"(ssr)/./node_modules/@radix-ui/react-tabs/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\nconst Tabs = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TabsList = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex h-12 items-center justify-center rounded-2xl bg-emerald-50 p-1 text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/tabs.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nTabsList.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.List.displayName;\nconst TabsTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center justify-center whitespace-nowrap rounded-xl px-4 py-2 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-emerald-500 data-[state=active]:text-white data-[state=active]:shadow-sm hover:bg-emerald-100\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/tabs.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nTabsTrigger.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Trigger.displayName;\nconst TabsContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mt-6 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ui/tabs.tsx\",\n        lineNumber: 42,\n        columnNumber: 3\n    }, undefined));\nTabsContent.displayName = _radix_ui_react_tabs__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/tabs.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNKO0FBRWpDLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QiIsInNvdXJjZXMiOlsid2VicGFjazovL2NyZWRpdC1jaGFrcmEtbGFuZGluZy1wYWdlLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4059915845b6\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY3JlZGl0LWNoYWtyYS1sYW5kaW5nLXBhZ2UvLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2QwZTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0MDU5OTE1ODQ1YjZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\",\"800\",\"900\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Poppins\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"300\\\",\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\",\\\"800\\\",\\\"900\\\"],\\\"variable\\\":\\\"--font-poppins\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"poppins\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Credit Chakra - Smarter MSME Credit Intelligence\",\n    description: \"AI-powered scoring, monitoring, and early-warning systems helping lenders and policymakers accelerate MSME growth.\",\n    keywords: \"MSME, credit scoring, AI, fintech, lending, risk assessment, financial technology\",\n    authors: [\n        {\n            name: \"Credit Chakra Team\"\n        }\n    ],\n    robots: \"index, follow\",\n    openGraph: {\n        title: \"Credit Chakra - Smarter MSME Credit Intelligence\",\n        description: \"AI-powered scoring, monitoring, and early-warning systems helping lenders and policymakers accelerate MSME growth.\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Credit Chakra - Smarter MSME Credit Intelligence\",\n        description: \"AI-powered scoring, monitoring, and early-warning systems helping lenders and policymakers accelerate MSME growth.\"\n    }\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Poppins_arguments_subsets_latin_weight_300_400_500_600_700_800_900_variable_font_poppins_display_swap_variableName_poppins___WEBPACK_IMPORTED_MODULE_3___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/layout.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/layout.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Hero__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/Hero */ \"(rsc)/./src/components/Hero.tsx\");\n/* harmony import */ var _components_ValuePropositions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ValuePropositions */ \"(rsc)/./src/components/ValuePropositions.tsx\");\n/* harmony import */ var _components_PlatformPreview__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/PlatformPreview */ \"(rsc)/./src/components/PlatformPreview.tsx\");\n/* harmony import */ var _components_HowItWorks__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/HowItWorks */ \"(rsc)/./src/components/HowItWorks.tsx\");\n/* harmony import */ var _components_ImpactMetrics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ImpactMetrics */ \"(rsc)/./src/components/ImpactMetrics.tsx\");\n/* harmony import */ var _components_UseCasesTabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/UseCasesTabs */ \"(rsc)/./src/components/UseCasesTabs.tsx\");\n/* harmony import */ var _components_Testimonials__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/Testimonials */ \"(rsc)/./src/components/Testimonials.tsx\");\n/* harmony import */ var _components_CTABanner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/CTABanner */ \"(rsc)/./src/components/CTABanner.tsx\");\n\n\n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Hero__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ValuePropositions__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PlatformPreview__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_HowItWorks__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ImpactMetrics__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_UseCasesTabs__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Testimonials__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CTABanner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/app/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/CTABanner.tsx":
/*!**************************************!*\
  !*** ./src/components/CTABanner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/CTABanner.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Hero.tsx":
/*!*********************************!*\
  !*** ./src/components/Hero.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Hero.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/HowItWorks.tsx":
/*!***************************************!*\
  !*** ./src/components/HowItWorks.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/HowItWorks.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/ImpactMetrics.tsx":
/*!******************************************!*\
  !*** ./src/components/ImpactMetrics.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ImpactMetrics.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/PlatformPreview.tsx":
/*!********************************************!*\
  !*** ./src/components/PlatformPreview.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/PlatformPreview.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/Testimonials.tsx":
/*!*****************************************!*\
  !*** ./src/components/Testimonials.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/Testimonials.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/UseCasesTabs.tsx":
/*!*****************************************!*\
  !*** ./src/components/UseCasesTabs.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/UseCasesTabs.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/components/ValuePropositions.tsx":
/*!**********************************************!*\
  !*** ./src/components/ValuePropositions.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Credit-Chakra-Final/credit-chakra/credit-chakra-landing-page/src/components/ValuePropositions.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/framer-motion","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fmanikumargouni%2FDocuments%2FCredit-Chakra-Final%2Fcredit-chakra%2Fcredit-chakra-landing-page&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();