# Credit Chakra Landing Page

A stunning, modern landing page for Credit Chakra built with Next.js, shadcn/ui, TailwindCSS, and Framer Motion.

## Features

- 🎨 **Modern Design**: Fintech-grade emerald green theme with premium animations
- 📱 **Fully Responsive**: Optimized for mobile, tablet, and desktop
- ⚡ **Performance**: Built with Next.js 14 and App Router
- 🎭 **Animations**: Smooth Framer Motion animations throughout
- 🎯 **Components**: Modular design with reusable components
- 🔧 **shadcn/ui**: Beautiful, accessible UI components

## Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Styling**: TailwindCSS with custom emerald theme
- **UI Components**: shadcn/ui
- **Animations**: Framer Motion
- **Typography**: Inter & Poppins fonts
- **Icons**: Lucide React

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Run the development server:
   ```bash
   npm run dev
   ```

3. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

```
src/
├── app/
│   ├── globals.css      # Global styles and animations
│   ├── layout.tsx       # Root layout
│   └── page.tsx         # Main landing page
├── components/
│   ├── ui/              # shadcn/ui components
│   ├── Hero.tsx         # Hero section
│   ├── ValuePropositions.tsx
│   ├── PlatformPreview.tsx
│   ├── HowItWorks.tsx
│   ├── ImpactMetrics.tsx
│   ├── UseCasesTabs.tsx
│   ├── Testimonials.tsx
│   └── CTABanner.tsx
└── lib/
    └── utils.ts         # Utility functions
```

## Design System

### Colors
- **Primary**: Emerald Green (#00B894)
- **Deep Green**: #005C4B
- **Accent Gold**: #FFD166
- **Background**: #F9FAFB

### Typography
- **Headings**: Poppins (Bold)
- **Body**: Inter (Clean)

### Animations
- Fade + slide-up for sections
- Staggered animations for cards
- Smooth hover effects
- Gradient background animations

## Deployment

The project is ready to deploy on Vercel:

```bash
npm run build
```

## License

MIT License - see LICENSE file for details.
