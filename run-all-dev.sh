#!/bin/bash

# Credit Chakra Complete Development Environment Script
# This script runs the FastAPI backend, Credit Chakra frontend, and MSME app simultaneously

echo "🚀 Starting Credit Chakra Complete Development Environment..."
echo "============================================================="

# Function to cleanup background processes on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down all development servers..."
    kill $BACKEND_PID $FRONTEND_PID $MSME_PID 2>/dev/null
    wait $BACKEND_PID $FRONTEND_PID $MSME_PID 2>/dev/null
    echo "✅ All development servers stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ Port $port is already in use. Please free the port and try again."
        exit 1
    fi
}

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Check if required ports are available
echo "🔍 Checking port availability..."
check_port 8000  # Backend
check_port 3000  # Credit Chakra Frontend
check_port 3001  # MSME App
echo "✅ All required ports are available"
echo ""

# Install backend dependencies if needed
echo "📦 Setting up backend..."
cd backend
if [ ! -d "venv" ]; then
    echo "🔧 Creating Python virtual environment..."
    python3 -m venv venv
fi

echo "🔧 Activating virtual environment and installing dependencies..."
source venv/bin/activate
pip install -r requirements.txt > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Backend dependencies installed"
else
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

# Start backend server
echo "🚀 Starting FastAPI backend server..."
python main.py &
BACKEND_PID=$!
echo "✅ Backend server started (PID: $BACKEND_PID)"
echo "📍 Backend URL: http://localhost:8000"
echo "📍 API Docs: http://localhost:8000/docs"
cd ..

# Install Credit Chakra frontend dependencies if needed
echo ""
echo "📦 Setting up Credit Chakra frontend..."
cd frontend
if [ ! -d "node_modules" ]; then
    echo "🔧 Installing Credit Chakra frontend dependencies..."
    npm install > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ Credit Chakra frontend dependencies installed"
    else
        echo "❌ Failed to install Credit Chakra frontend dependencies"
        kill $BACKEND_PID
        exit 1
    fi
else
    echo "✅ Credit Chakra frontend dependencies already installed"
fi

# Start Credit Chakra frontend server on port 3000
echo "🚀 Starting Credit Chakra frontend server..."
npm run dev &
FRONTEND_PID=$!
echo "✅ Credit Chakra frontend server started (PID: $FRONTEND_PID)"
echo "📍 Credit Chakra Frontend URL: http://localhost:3000"
cd ..

# Install MSME app dependencies if needed
echo ""
echo "📦 Setting up MSME application..."
cd msme-app
if [ ! -d "node_modules" ]; then
    echo "🔧 Installing MSME app dependencies..."
    npm install > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ MSME app dependencies installed"
    else
        echo "❌ Failed to install MSME app dependencies"
        kill $BACKEND_PID $FRONTEND_PID
        exit 1
    fi
else
    echo "✅ MSME app dependencies already installed"
fi

# Start MSME app server on port 3001
echo "🚀 Starting MSME application server..."
npm run dev -- --port 3001 &
MSME_PID=$!
echo "✅ MSME application server started (PID: $MSME_PID)"
echo "📍 MSME App URL: http://localhost:3001"
cd ..

echo ""
echo "🎉 Complete development environment is ready!"
echo "============================================================="
echo "🏦 Credit Chakra Frontend (Manager Dashboard): http://localhost:3000"
echo "📱 MSME Application (Business Portal): http://localhost:3001"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Documentation: http://localhost:8000/docs"
echo "============================================================="
echo ""
echo "💡 Demo Login Credentials:"
echo "   Credit Manager Dashboard:"
echo "     Email: <EMAIL>"
echo "     Password: manager123"
echo ""
echo "   MSME Business Portal:"
echo "     Email: <EMAIL>"
echo "     Password: demo123"
echo ""
echo "🔄 All servers are running. Press Ctrl+C to stop all servers."
echo ""

# Wait for all processes
wait $BACKEND_PID $FRONTEND_PID $MSME_PID
