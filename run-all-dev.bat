@echo off
setlocal enabledelayedexpansion

REM Credit Chakra Complete Development Environment Script (Windows)
REM This script runs the FastAPI backend, Credit Chakra frontend, and MSME app simultaneously

echo 🚀 Starting Credit Chakra Complete Development Environment...
echo =============================================================

REM Function to check if command exists
where python >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Python is not installed. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Check if required ports are available
echo 🔍 Checking port availability...
netstat -an | find "LISTENING" | find ":8000" >nul
if %errorlevel% equ 0 (
    echo ❌ Port 8000 is already in use. Please free the port and try again.
    pause
    exit /b 1
)

netstat -an | find "LISTENING" | find ":3000" >nul
if %errorlevel% equ 0 (
    echo ❌ Port 3000 is already in use. Please free the port and try again.
    pause
    exit /b 1
)

netstat -an | find "LISTENING" | find ":3001" >nul
if %errorlevel% equ 0 (
    echo ❌ Port 3001 is already in use. Please free the port and try again.
    pause
    exit /b 1
)

echo ✅ All required ports are available
echo.

REM Setup backend
echo 📦 Setting up backend...
cd backend

if not exist "venv" (
    echo 🔧 Creating Python virtual environment...
    python -m venv venv
)

echo 🔧 Activating virtual environment and installing dependencies...
call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ Backend dependencies installed
) else (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
)

REM Start backend server
echo 🚀 Starting FastAPI backend server...
start "Backend Server" cmd /k "venv\Scripts\activate.bat && python main.py"
echo ✅ Backend server started
echo 📍 Backend URL: http://localhost:8000
echo 📍 API Docs: http://localhost:8000/docs
cd ..

REM Setup Credit Chakra frontend
echo.
echo 📦 Setting up Credit Chakra frontend...
cd frontend

if not exist "node_modules" (
    echo 🔧 Installing Credit Chakra frontend dependencies...
    npm install >nul 2>&1
    
    if !errorlevel! equ 0 (
        echo ✅ Credit Chakra frontend dependencies installed
    ) else (
        echo ❌ Failed to install Credit Chakra frontend dependencies
        pause
        exit /b 1
    )
) else (
    echo ✅ Credit Chakra frontend dependencies already installed
)

REM Start Credit Chakra frontend server
echo 🚀 Starting Credit Chakra frontend server...
start "Credit Chakra Frontend" cmd /k "npm run dev"
echo ✅ Credit Chakra frontend server started
echo 📍 Credit Chakra Frontend URL: http://localhost:3000
cd ..

REM Setup MSME app
echo.
echo 📦 Setting up MSME application...
cd msme-app

if not exist "node_modules" (
    echo 🔧 Installing MSME app dependencies...
    npm install >nul 2>&1
    
    if !errorlevel! equ 0 (
        echo ✅ MSME app dependencies installed
    ) else (
        echo ❌ Failed to install MSME app dependencies
        pause
        exit /b 1
    )
) else (
    echo ✅ MSME app dependencies already installed
)

REM Start MSME app server
echo 🚀 Starting MSME application server...
start "MSME Application" cmd /k "npm run dev -- --port 3001"
echo ✅ MSME application server started
echo 📍 MSME App URL: http://localhost:3001
cd ..

echo.
echo 🎉 Complete development environment is ready!
echo =============================================================
echo 🏦 Credit Chakra Frontend (Manager Dashboard): http://localhost:3000
echo 📱 MSME Application (Business Portal): http://localhost:3001
echo 🔧 Backend API: http://localhost:8000
echo 📚 API Documentation: http://localhost:8000/docs
echo =============================================================
echo.
echo 💡 Demo Login Credentials:
echo    Credit Manager Dashboard:
echo      Email: <EMAIL>
echo      Password: manager123
echo.
echo    MSME Business Portal:
echo      Email: <EMAIL>
echo      Password: demo123
echo.
echo 🔄 All servers are running in separate windows.
echo 📝 Close the individual command windows to stop each server.
echo.
pause
