#!/bin/bash

# Credit Chakra Development Server Script
# This script runs both the FastAPI backend and Next.js frontend simultaneously

echo "🚀 Starting Credit Chakra Development Environment..."
echo "=================================================="

# Function to cleanup background processes on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down development servers..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    wait $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo "✅ Development servers stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

# Check if npm is available
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm."
    exit 1
fi

echo "✅ Prerequisites check passed"
echo ""

# Install backend dependencies if needed
echo "📦 Checking backend dependencies..."
cd backend
if [ ! -d "venv" ]; then
    echo "🔧 Creating Python virtual environment..."
    python3 -m venv venv
fi

echo "🔧 Activating virtual environment and installing dependencies..."
source venv/bin/activate
pip install -r requirements.txt > /dev/null 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Backend dependencies installed"
else
    echo "❌ Failed to install backend dependencies"
    exit 1
fi

# Start backend server
echo "🚀 Starting FastAPI backend server..."
python main.py &
BACKEND_PID=$!
echo "✅ Backend server started (PID: $BACKEND_PID)"
echo "📍 Backend URL: http://localhost:8000"
echo "📍 API Docs: http://localhost:8000/docs"
cd ..

# Install frontend dependencies if needed
echo ""
echo "📦 Checking frontend dependencies..."
cd msme-app
if [ ! -d "node_modules" ]; then
    echo "🔧 Installing frontend dependencies..."
    npm install > /dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "✅ Frontend dependencies installed"
    else
        echo "❌ Failed to install frontend dependencies"
        kill $BACKEND_PID
        exit 1
    fi
else
    echo "✅ Frontend dependencies already installed"
fi

# Start frontend server
echo "🚀 Starting Next.js frontend server..."
npm run dev &
FRONTEND_PID=$!
echo "✅ Frontend server started (PID: $FRONTEND_PID)"
echo "📍 Frontend URL: http://localhost:3000"
cd ..

echo ""
echo "🎉 Development environment is ready!"
echo "=================================================="
echo "📱 Frontend (MSME App): http://localhost:3000"
echo "🔧 Backend API: http://localhost:8000"
echo "📚 API Documentation: http://localhost:8000/docs"
echo "=================================================="
echo ""
echo "💡 Demo Login Credentials:"
echo "   Email: <EMAIL>"
echo "   Password: demo123"
echo ""
echo "🔄 Both servers are running. Press Ctrl+C to stop all servers."
echo ""

# Wait for both processes
wait $BACKEND_PID $FRONTEND_PID
