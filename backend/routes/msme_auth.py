"""
MSME Authentication Routes for MSME-facing application.

Provides endpoints for:
- MSME user registration and login
- Profile management
- Role-based access control
- Email/phone verification

Author: Credit Chakra Team
Version: 1.0.0
"""
from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, EmailStr
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
import uuid
import logging
import jwt
import hashlib
import secrets

from models.msme import MSMEProfile, BusinessType
from firebase.init import get_firestore_client
from utils.error_handler import handle_common_exceptions, ValidationError

logger = logging.getLogger(__name__)

router = APIRouter()
security = HTTPBearer()

# JWT Configuration (in production, use environment variables)
JWT_SECRET = "your-secret-key-here"  # Should be in environment variables
JWT_ALGORITHM = "HS256"
JWT_EXPIRATION_HOURS = 24

class MSMERegistrationRequest(BaseModel):
    """MSME user registration request"""
    # Business details
    business_name: str = Field(..., min_length=1, max_length=200)
    business_type: BusinessType
    location: str = Field(..., min_length=1, max_length=100)
    gst_number: Optional[str] = Field(None, pattern=r'^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$')
    
    # Contact details
    email: EmailStr
    phone: str = Field(..., pattern=r'^\+?[1-9]\d{1,14}$')
    
    # Authentication
    password: str = Field(..., min_length=8, max_length=100)
    
    # Optional fields
    tags: Optional[list] = Field(default_factory=list)

class MSMELoginRequest(BaseModel):
    """MSME user login request"""
    email: EmailStr
    password: str = Field(..., min_length=1)

class MSMEProfileUpdate(BaseModel):
    """MSME profile update request"""
    business_name: Optional[str] = Field(None, min_length=1, max_length=200)
    business_type: Optional[BusinessType] = None
    location: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None, pattern=r'^\+?[1-9]\d{1,14}$')
    tags: Optional[list] = None

class PasswordChangeRequest(BaseModel):
    """Password change request"""
    current_password: str = Field(..., min_length=1)
    new_password: str = Field(..., min_length=8, max_length=100)

class VerificationRequest(BaseModel):
    """Email/phone verification request"""
    verification_code: str = Field(..., min_length=6, max_length=6)
    verification_type: str = Field(..., pattern=r'^(email|phone)$')

def hash_password(password: str) -> str:
    """Hash password using SHA-256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    """Verify password against hash"""
    return hash_password(password) == hashed

def generate_jwt_token(msme_id: str, email: str) -> str:
    """Generate JWT token for authenticated user"""
    payload = {
        "msme_id": msme_id,
        "email": email,
        "exp": datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS),
        "iat": datetime.utcnow()
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def verify_jwt_token(token: str) -> Dict[str, Any]:
    """Verify and decode JWT token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

async def get_current_msme(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Dict[str, Any]:
    """Dependency to get current authenticated MSME"""
    token = credentials.credentials
    payload = verify_jwt_token(token)
    
    # Verify MSME still exists and is active
    db = get_firestore_client()
    msme_doc = db.collection('msmes').document(payload['msme_id']).get()
    
    if not msme_doc.exists:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="MSME account not found"
        )
    
    msme_data = msme_doc.to_dict()
    msme_data['msme_id'] = payload['msme_id']
    
    return msme_data

@router.post("/register", response_model=dict)
@handle_common_exceptions
async def register_msme(registration_data: MSMERegistrationRequest):
    """
    Register a new MSME user account.
    
    Creates MSME profile and authentication credentials.
    """
    try:
        db = get_firestore_client()
        
        # Check if email already exists
        existing_msmes = db.collection('msmes').where('email', '==', registration_data.email).limit(1).stream()
        if any(existing_msmes):
            raise ValidationError("Email address already registered")
        
        # Generate MSME ID
        msme_id = str(uuid.uuid4())
        
        # Hash password
        password_hash = hash_password(registration_data.password)
        
        # Generate verification codes
        email_verification_code = secrets.token_hex(3).upper()
        phone_verification_code = secrets.token_hex(3).upper()
        
        # Create MSME profile
        msme_profile = {
            'msme_id': msme_id,
            'name': registration_data.business_name,
            'business_type': registration_data.business_type.value,
            'location': registration_data.location,
            'gst_number': registration_data.gst_number,
            'email': registration_data.email,
            'phone': registration_data.phone,
            'password_hash': password_hash,
            'tags': registration_data.tags or [],
            'created_at': datetime.now(),
            'is_verified': False,
            'email_verified': False,
            'phone_verified': False,
            'email_verification_code': email_verification_code,
            'phone_verification_code': phone_verification_code,
            'verification_code_expires': datetime.now() + timedelta(hours=24),
            'score': 0.0,
            'ccr_score': 0.0,
            'risk_band': 'red',
            'last_login': None,
            'login_count': 0
        }
        
        # Save to Firestore
        db.collection('msmes').document(msme_id).set(msme_profile)
        
        # Generate JWT token
        token = generate_jwt_token(msme_id, registration_data.email)
        
        # In production, send verification emails/SMS here
        logger.info(f"MSME registered: {msme_id}, Email verification code: {email_verification_code}")
        
        return {
            "message": "MSME registered successfully",
            "msme_id": msme_id,
            "access_token": token,
            "token_type": "bearer",
            "expires_in": JWT_EXPIRATION_HOURS * 3600,
            "verification_required": True,
            "verification_methods": ["email", "phone"]
        }
        
    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Error registering MSME: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Registration failed: {str(e)}"
        )

@router.post("/login", response_model=dict)
@handle_common_exceptions
async def login_msme(login_data: MSMELoginRequest):
    """
    Authenticate MSME user and return access token.
    """
    try:
        db = get_firestore_client()
        
        # Find MSME by email
        msmes = db.collection('msmes').where('email', '==', login_data.email).limit(1).stream()
        msme_doc = None
        
        for doc in msmes:
            msme_doc = doc
            break
        
        if not msme_doc:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        msme_data = msme_doc.to_dict()
        msme_id = msme_doc.id
        
        # Verify password
        if not verify_password(login_data.password, msme_data.get('password_hash', '')):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Update login statistics
        db.collection('msmes').document(msme_id).update({
            'last_login': datetime.now(),
            'login_count': msme_data.get('login_count', 0) + 1
        })
        
        # Generate JWT token
        token = generate_jwt_token(msme_id, login_data.email)
        
        return {
            "message": "Login successful",
            "msme_id": msme_id,
            "business_name": msme_data.get('name'),
            "access_token": token,
            "token_type": "bearer",
            "expires_in": JWT_EXPIRATION_HOURS * 3600,
            "is_verified": msme_data.get('is_verified', False),
            "verification_required": not msme_data.get('is_verified', False)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during login: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Login failed: {str(e)}"
        )

@router.get("/profile", response_model=dict)
@handle_common_exceptions
async def get_msme_profile(current_msme: dict = Depends(get_current_msme)):
    """
    Get current MSME user profile.
    """
    # Remove sensitive information
    profile = current_msme.copy()
    profile.pop('password_hash', None)
    profile.pop('email_verification_code', None)
    profile.pop('phone_verification_code', None)
    
    return {
        "msme_id": profile.get('msme_id'),
        "business_name": profile.get('name'),
        "business_type": profile.get('business_type'),
        "location": profile.get('location'),
        "email": profile.get('email'),
        "phone": profile.get('phone'),
        "gst_number": profile.get('gst_number'),
        "tags": profile.get('tags', []),
        "is_verified": profile.get('is_verified', False),
        "email_verified": profile.get('email_verified', False),
        "phone_verified": profile.get('phone_verified', False),
        "ccr_score": profile.get('ccr_score', 0.0),
        "risk_band": profile.get('risk_band', 'red'),
        "created_at": profile.get('created_at'),
        "last_login": profile.get('last_login')
    }

@router.put("/profile", response_model=dict)
@handle_common_exceptions
async def update_msme_profile(
    update_data: MSMEProfileUpdate,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Update MSME user profile.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']
        
        # Prepare update data
        update_fields = {}
        if update_data.business_name:
            update_fields['name'] = update_data.business_name
        if update_data.business_type:
            update_fields['business_type'] = update_data.business_type.value
        if update_data.location:
            update_fields['location'] = update_data.location
        if update_data.phone:
            update_fields['phone'] = update_data.phone
            update_fields['phone_verified'] = False  # Re-verify if phone changed
        if update_data.tags is not None:
            update_fields['tags'] = update_data.tags
        
        if update_fields:
            update_fields['updated_at'] = datetime.now()
            db.collection('msmes').document(msme_id).update(update_fields)
        
        return {
            "message": "Profile updated successfully",
            "updated_fields": list(update_fields.keys())
        }
        
    except Exception as e:
        logger.error(f"Error updating profile for MSME {current_msme['msme_id']}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Profile update failed: {str(e)}"
        )
