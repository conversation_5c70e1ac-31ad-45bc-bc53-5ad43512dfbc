"""
Training Module API Routes for MSME-facing application.

Provides endpoints for:
- Course discovery and enrollment
- Content delivery and progress tracking
- Quiz management and assessment
- Certification and achievements

Author: Credit Chakra Team
Version: 1.0.0
"""
from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import uuid
import logging

from models.training import (
    TrainingCourse, TrainingModule, UserCourseProgress, Certificate,
    CourseEnrollRequest, ModuleCompletionRequest, QuizSubmissionRequest, QuizResult,
    CourseStatus, DifficultyLevel
)
from routes.msme_auth import get_current_msme
from firebase.init import get_firestore_client
from utils.error_handler import handle_common_exceptions, ValidationError

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/courses", response_model=dict)
@handle_common_exceptions
async def get_available_courses(
    category: Optional[str] = Query(None, description="Filter by course category"),
    difficulty: Optional[DifficultyLevel] = Query(None, description="Filter by difficulty level"),
    business_type: Optional[str] = Query(None, description="Filter by target business type"),
    page: int = Query(default=1, ge=1),
    limit: int = Query(default=20, ge=1, le=100),
    current_msme: dict = Depends(get_current_msme)
):
    """
    Get available training courses with filtering and pagination.
    """
    try:
        db = get_firestore_client()
        
        # Build query
        courses_ref = db.collection('training_courses').where('is_active', '==', True)
        
        if category:
            courses_ref = courses_ref.where('category', '==', category)
        if difficulty:
            courses_ref = courses_ref.where('difficulty_level', '==', difficulty.value)
        
        # Get courses
        courses_docs = courses_ref.order_by('created_at', direction='DESCENDING').limit(limit).offset((page - 1) * limit).stream()
        
        courses = []
        msme_id = current_msme['msme_id']
        
        for doc in courses_docs:
            course_data = doc.to_dict()
            course_data['course_id'] = doc.id
            
            # Check if user is enrolled
            progress_doc = db.collection('user_course_progress').where('msme_id', '==', msme_id).where('course_id', '==', doc.id).limit(1).get()
            enrollment_status = "not_enrolled"
            progress_percentage = 0
            
            if progress_doc:
                progress_data = progress_doc[0].to_dict()
                enrollment_status = progress_data.get('status', 'not_started')
                completed_modules = len(progress_data.get('completed_modules', []))
                total_modules = len(course_data.get('modules', []))
                progress_percentage = (completed_modules / total_modules * 100) if total_modules > 0 else 0
            
            # Filter by business type if specified
            if business_type:
                target_types = course_data.get('target_business_types', [])
                if target_types and business_type not in target_types:
                    continue
            
            courses.append({
                "course_id": course_data['course_id'],
                "title": course_data.get('title'),
                "description": course_data.get('description'),
                "category": course_data.get('category'),
                "difficulty_level": course_data.get('difficulty_level'),
                "estimated_duration_minutes": course_data.get('estimated_duration_minutes'),
                "has_certificate": course_data.get('has_certificate', False),
                "enrollment_status": enrollment_status,
                "progress_percentage": round(progress_percentage, 1),
                "module_count": len(course_data.get('modules', [])),
                "tags": course_data.get('tags', [])
            })
        
        # Get total count for pagination
        total_courses = len(list(db.collection('training_courses').where('is_active', '==', True).stream()))
        
        return {
            "courses": courses,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_courses,
                "pages": (total_courses + limit - 1) // limit
            },
            "filters_applied": {
                "category": category,
                "difficulty": difficulty.value if difficulty else None,
                "business_type": business_type
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting courses: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get courses: {str(e)}"
        )

@router.post("/courses/{course_id}/enroll", response_model=dict)
@handle_common_exceptions
async def enroll_in_course(
    course_id: str,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Enroll MSME user in a training course.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']
        
        # Verify course exists
        course_doc = db.collection('training_courses').document(course_id).get()
        if not course_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Course not found"
            )
        
        course_data = course_doc.to_dict()
        
        # Check if already enrolled
        existing_progress = db.collection('user_course_progress').where('msme_id', '==', msme_id).where('course_id', '==', course_id).limit(1).get()
        if existing_progress:
            raise ValidationError("Already enrolled in this course")
        
        # Check prerequisites
        prerequisites = course_data.get('prerequisites', [])
        if prerequisites:
            for prereq_id in prerequisites:
                prereq_progress = db.collection('user_course_progress').where('msme_id', '==', msme_id).where('course_id', '==', prereq_id).where('status', '==', 'completed').limit(1).get()
                if not prereq_progress:
                    raise ValidationError(f"Prerequisite course {prereq_id} not completed")
        
        # Create progress record
        progress_id = str(uuid.uuid4())
        progress_data = {
            'progress_id': progress_id,
            'msme_id': msme_id,
            'course_id': course_id,
            'status': 'not_started',
            'started_at': datetime.now(),
            'completed_modules': [],
            'current_module_id': course_data.get('modules', [None])[0],
            'quiz_attempts': [],
            'best_score': None,
            'total_time_spent_minutes': 0,
            'certificate_issued': False
        }
        
        db.collection('user_course_progress').document(progress_id).set(progress_data)
        
        return {
            "message": "Successfully enrolled in course",
            "course_id": course_id,
            "course_title": course_data.get('title'),
            "progress_id": progress_id,
            "status": "enrolled",
            "next_module": course_data.get('modules', [None])[0]
        }
        
    except ValidationError:
        raise
    except Exception as e:
        logger.error(f"Error enrolling in course {course_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Enrollment failed: {str(e)}"
        )

@router.get("/courses/{course_id}/modules", response_model=dict)
@handle_common_exceptions
async def get_course_modules(
    course_id: str,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Get modules for a specific course with progress tracking.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']
        
        # Verify enrollment
        progress_docs = db.collection('user_course_progress').where('msme_id', '==', msme_id).where('course_id', '==', course_id).limit(1).get()
        if not progress_docs:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enrolled in this course"
            )
        
        progress_data = progress_docs[0].to_dict()
        completed_modules = progress_data.get('completed_modules', [])
        
        # Get course modules
        modules_docs = db.collection('training_modules').where('course_id', '==', course_id).order_by('order_index').stream()
        
        modules = []
        for doc in modules_docs:
            module_data = doc.to_dict()
            module_id = doc.id
            
            modules.append({
                "module_id": module_id,
                "title": module_data.get('title'),
                "description": module_data.get('description'),
                "order_index": module_data.get('order_index'),
                "content_type": module_data.get('content_type'),
                "duration_minutes": module_data.get('duration_minutes'),
                "is_mandatory": module_data.get('is_mandatory', True),
                "is_completed": module_id in completed_modules,
                "is_accessible": module_data.get('order_index', 0) == 0 or 
                               any(m_id in completed_modules for m_id in modules[:module_data.get('order_index', 0)])
            })
        
        return {
            "course_id": course_id,
            "modules": modules,
            "progress": {
                "completed_modules": len(completed_modules),
                "total_modules": len(modules),
                "completion_percentage": (len(completed_modules) / len(modules) * 100) if modules else 0,
                "current_module": progress_data.get('current_module_id'),
                "status": progress_data.get('status')
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting course modules: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get modules: {str(e)}"
        )

@router.get("/modules/{module_id}/content", response_model=dict)
@handle_common_exceptions
async def get_module_content(
    module_id: str,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Get content for a specific training module.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']
        
        # Get module
        module_doc = db.collection('training_modules').document(module_id).get()
        if not module_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Module not found"
            )
        
        module_data = module_doc.to_dict()
        course_id = module_data.get('course_id')
        
        # Verify enrollment
        progress_docs = db.collection('user_course_progress').where('msme_id', '==', msme_id).where('course_id', '==', course_id).limit(1).get()
        if not progress_docs:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enrolled in this course"
            )
        
        # Check if module is accessible (previous modules completed)
        progress_data = progress_docs[0].to_dict()
        completed_modules = progress_data.get('completed_modules', [])
        module_order = module_data.get('order_index', 0)
        
        if module_order > 0:
            # Check if previous modules are completed
            prev_modules = db.collection('training_modules').where('course_id', '==', course_id).where('order_index', '<', module_order).stream()
            for prev_module in prev_modules:
                if prev_module.id not in completed_modules and prev_module.to_dict().get('is_mandatory', True):
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="Previous modules must be completed first"
                    )
        
        # Return module content
        content_data = {
            "module_id": module_id,
            "title": module_data.get('title'),
            "description": module_data.get('description'),
            "content_type": module_data.get('content_type'),
            "duration_minutes": module_data.get('duration_minutes'),
            "content_url": module_data.get('content_url'),
            "content_data": module_data.get('content_data', {}),
            "interactive_elements": module_data.get('interactive_elements', []),
            "completion_criteria": module_data.get('completion_criteria', {}),
            "is_completed": module_id in completed_modules
        }
        
        # Handle SCORM content
        if module_data.get('content_type') == 'scorm':
            content_data.update({
                "scorm_package_url": module_data.get('scorm_package_url'),
                "scorm_version": module_data.get('scorm_version')
            })
        
        return content_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting module content: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get module content: {str(e)}"
        )

@router.post("/modules/{module_id}/complete", response_model=dict)
@handle_common_exceptions
async def complete_module(
    module_id: str,
    completion_data: ModuleCompletionRequest,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Mark a training module as completed.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']
        
        # Get module
        module_doc = db.collection('training_modules').document(module_id).get()
        if not module_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Module not found"
            )
        
        module_data = module_doc.to_dict()
        course_id = module_data.get('course_id')
        
        # Get progress record
        progress_docs = db.collection('user_course_progress').where('msme_id', '==', msme_id).where('course_id', '==', course_id).limit(1).get()
        if not progress_docs:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enrolled in this course"
            )
        
        progress_doc = progress_docs[0]
        progress_data = progress_doc.to_dict()
        
        # Update progress
        completed_modules = progress_data.get('completed_modules', [])
        if module_id not in completed_modules:
            completed_modules.append(module_id)
        
        # Update total time spent
        total_time = progress_data.get('total_time_spent_minutes', 0) + completion_data.time_spent_minutes
        
        # Find next module
        next_modules = db.collection('training_modules').where('course_id', '==', course_id).where('order_index', '>', module_data.get('order_index', 0)).order_by('order_index').limit(1).get()
        next_module_id = next_modules[0].id if next_modules else None
        
        # Check if course is completed
        all_modules = list(db.collection('training_modules').where('course_id', '==', course_id).stream())
        mandatory_modules = [m.id for m in all_modules if m.to_dict().get('is_mandatory', True)]
        course_completed = all(m_id in completed_modules for m_id in mandatory_modules)
        
        # Update progress record
        update_data = {
            'completed_modules': completed_modules,
            'total_time_spent_minutes': total_time,
            'current_module_id': next_module_id,
            'last_accessed': datetime.now()
        }
        
        if course_completed:
            update_data.update({
                'status': 'completed',
                'completed_at': datetime.now()
            })
        elif progress_data.get('status') == 'not_started':
            update_data['status'] = 'in_progress'
        
        db.collection('user_course_progress').document(progress_doc.id).update(update_data)
        
        return {
            "message": "Module completed successfully",
            "module_id": module_id,
            "course_completed": course_completed,
            "next_module_id": next_module_id,
            "progress": {
                "completed_modules": len(completed_modules),
                "total_modules": len(all_modules),
                "completion_percentage": (len(completed_modules) / len(all_modules) * 100) if all_modules else 0
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error completing module {module_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to complete module: {str(e)}"
        )
