from fastapi import APIRouter, HTTPException, status, Depends
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone, timedelta
import uuid
from pydantic import BaseModel, Field

from models.nudge import Nudge, NudgeCreate, NudgeUpdate, NudgeStatus, NudgeRequest, BehavioralNudge, TriggerType, NudgeCategory, Medium
from firebase.init import get_firestore_client
from routes.msme_auth import get_current_msme

router = APIRouter()

# Bulk nudge models
class BulkNudgeRequest(BaseModel):
    msme_ids: List[str]
    trigger_type: str
    message: str
    medium: str
    metadata: Optional[Dict[str, Any]] = None

class BulkNudgeResponse(BaseModel):
    total_requested: int
    successful: int
    failed: int
    nudge_ids: List[str]
    errors: List[Dict[str, str]]

# Bulk nudge endpoint
@router.post("/bulk", response_model=BulkNudgeResponse, status_code=status.HTTP_201_CREATED)
async def send_bulk_nudges(bulk_request: BulkNudgeRequest):
    """Send nudges to multiple MSMEs"""
    try:
        db = get_firestore_client()

        successful_nudges = []
        failed_nudges = []
        errors = []

        for msme_id in bulk_request.msme_ids:
            try:
                # Verify MSME exists
                msme_ref = db.collection('msmes').document(msme_id)
                msme_doc = msme_ref.get()

                if not msme_doc.exists:
                    errors.append({
                        "msme_id": msme_id,
                        "error": "MSME profile not found"
                    })
                    failed_nudges.append(msme_id)
                    continue

                # Generate unique nudge ID
                nudge_id = str(uuid.uuid4())

                # Create nudge data
                nudge_data = {
                    'nudge_id': nudge_id,
                    'msme_id': msme_id,
                    'trigger_type': bulk_request.trigger_type,
                    'message': bulk_request.message,
                    'medium': bulk_request.medium,
                    'sent_at': datetime.now(timezone.utc).isoformat(),
                    'status': 'sent',
                    'metadata': bulk_request.metadata or {}
                }

                # Save nudge to Firestore
                nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
                nudge_ref.set(nudge_data)

                successful_nudges.append(nudge_id)

            except Exception as e:
                errors.append({
                    "msme_id": msme_id,
                    "error": str(e)
                })
                failed_nudges.append(msme_id)

        return BulkNudgeResponse(
            total_requested=len(bulk_request.msme_ids),
            successful=len(successful_nudges),
            failed=len(failed_nudges),
            nudge_ids=successful_nudges,
            errors=errors
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send bulk nudges: {str(e)}"
        )

# New MSME-centric endpoints for Day 3
@router.post("/msme/{msme_id}/nudges", response_model=Nudge, status_code=status.HTTP_201_CREATED)
async def send_nudge_to_msme(msme_id: str, nudge_request: NudgeRequest):
    """Send a nudge to an MSME"""
    try:
        db = get_firestore_client()

        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()

        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())

        # Create nudge
        nudge_data = {
            'nudge_id': nudge_id,
            'msme_id': msme_id,
            'trigger_type': nudge_request.trigger_type,
            'message': nudge_request.message,
            'medium': nudge_request.medium,
            'sent_at': datetime.now(timezone.utc).isoformat(),
            'status': 'sent',  # For now, assume immediate sending
            'metadata': nudge_request.metadata
        }

        # Save nudge to Firestore
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_ref.set(nudge_data)

        # Return created nudge
        return Nudge(**nudge_data)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send nudge: {str(e)}"
        )

@router.get("/msme/{msme_id}/nudges", response_model=List[dict])
async def get_msme_nudges(msme_id: str, limit: Optional[int] = 20):
    """Get nudges for an MSME ordered by sent_at descending"""
    try:
        db = get_firestore_client()

        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()

        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )

        # Get nudges ordered by sent_at descending
        nudges_ref = msme_ref.collection('nudges').order_by('sent_at', direction='DESCENDING')

        if limit:
            nudges_ref = nudges_ref.limit(limit)

        nudges_docs = nudges_ref.stream()

        nudges = []
        for doc in nudges_docs:
            nudge_data = doc.to_dict()
            nudges.append(nudge_data)

        return nudges

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch nudges: {str(e)}"
        )

# Existing endpoints for backward compatibility
@router.post("/send", response_model=Nudge, status_code=status.HTTP_201_CREATED)
async def send_nudge(nudge_data: NudgeCreate):
    """Send a nudge to an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(nudge_data.msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Generate unique nudge ID
        nudge_id = str(uuid.uuid4())
        
        # Create nudge
        nudge = Nudge(
            nudge_id=nudge_id,
            msme_id=nudge_data.msme_id,
            trigger_type=nudge_data.trigger_type,
            message=nudge_data.message,
            medium=nudge_data.medium,
            sent_at=datetime.now(timezone.utc),
            status=NudgeStatus.SENT,  # For now, assume immediate sending
            metadata=nudge_data.metadata
        )
        
        # Save nudge to Firestore
        nudge_ref = db.collection('msmes').document(nudge_data.msme_id).collection('nudges').document(nudge_id)
        nudge_ref.set(nudge.dict())
        
        # TODO: Implement actual sending logic based on medium
        # For now, just mark as sent
        
        return nudge
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to send nudge: {str(e)}"
        )

@router.get("/{msme_id}", response_model=List[Nudge])
async def get_nudges(msme_id: str, status_filter: Optional[str] = None, limit: int = 50):
    """Get nudges for an MSME"""
    try:
        db = get_firestore_client()
        
        # Verify MSME exists
        msme_ref = db.collection('msmes').document(msme_id)
        msme_doc = msme_ref.get()
        
        if not msme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MSME profile not found"
            )
        
        # Build query
        query = db.collection('msmes').document(msme_id).collection('nudges')
        
        if status_filter:
            query = query.where('status', '==', status_filter)
        
        query = query.order_by('sent_at', direction='DESCENDING').limit(limit)
        
        # Execute query
        docs = query.stream()
        
        nudges = []
        for doc in docs:
            data = doc.to_dict()
            nudges.append(Nudge(**data))
        
        return nudges
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve nudges: {str(e)}"
        )

@router.put("/{msme_id}/{nudge_id}", response_model=Nudge)
async def update_nudge(msme_id: str, nudge_id: str, update_data: NudgeUpdate):
    """Update nudge status or content"""
    try:
        db = get_firestore_client()
        
        nudge_ref = db.collection('msmes').document(msme_id).collection('nudges').document(nudge_id)
        nudge_doc = nudge_ref.get()
        
        if not nudge_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nudge not found"
            )
        
        # Update only provided fields
        update_dict = {k: v for k, v in update_data.dict().items() if v is not None}
        nudge_ref.update(update_dict)
        
        # Return updated nudge
        updated_doc = nudge_ref.get()
        data = updated_doc.to_dict()
        return Nudge(**data)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update nudge: {str(e)}"
        )

# MSME-facing behavioral nudge endpoints
class BehavioralNudgeRequest(BaseModel):
    """Request model for creating behavioral nudges"""
    trigger_type: TriggerType
    category: NudgeCategory
    message: str = Field(..., min_length=1, max_length=1000)
    action_text: Optional[str] = Field(None, max_length=50)
    action_url: Optional[str] = None
    medium: Medium = Medium.IN_APP
    scheduled_at: Optional[datetime] = None
    target_score_component: Optional[str] = None

@router.post("/behavioral", response_model=dict)
async def create_behavioral_nudge(
    nudge_request: BehavioralNudgeRequest,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Create a personalized behavioral nudge for the authenticated MSME user.

    This endpoint creates nudges with enhanced personalization and behavioral triggers
    specifically designed to improve CCR score components.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']

        # Generate nudge ID
        nudge_id = str(uuid.uuid4())

        # Get MSME profile for personalization
        msme_doc = db.collection('msmes').document(msme_id).get()
        msme_data = msme_doc.to_dict() if msme_doc.exists else {}

        # Create personalization data
        personalization_data = {
            "business_name": msme_data.get('name', 'Your Business'),
            "business_type": msme_data.get('business_type', 'business'),
            "location": msme_data.get('location', ''),
            "current_ccr_score": msme_data.get('ccr_score', 0),
            "risk_band": msme_data.get('risk_band', 'red')
        }

        # Personalize message
        personalized_message = _personalize_nudge_message(
            nudge_request.message,
            personalization_data
        )

        # Create behavioral nudge
        behavioral_nudge = BehavioralNudge(
            nudge_id=nudge_id,
            msme_id=msme_id,
            trigger_type=nudge_request.trigger_type,
            category=nudge_request.category,
            title=_generate_nudge_title(nudge_request.category, nudge_request.trigger_type),
            message=personalized_message,
            action_text=nudge_request.action_text,
            action_url=nudge_request.action_url,
            medium=nudge_request.medium,
            scheduled_at=nudge_request.scheduled_at,
            target_score_component=nudge_request.target_score_component,
            personalization_data=personalization_data,
            user_context={
                "last_login": datetime.now().isoformat(),
                "device_type": "web",
                "session_id": str(uuid.uuid4())
            },
            trigger_conditions={
                "score_threshold": 70,
                "days_since_last_action": 7,
                "component_score_below": 50
            },
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(days=30)
        )

        # Save to Firestore
        nudge_ref = db.collection('msmes').document(msme_id).collection('behavioral_nudges').document(nudge_id)
        nudge_ref.set(behavioral_nudge.dict())

        # Schedule delivery if needed
        if nudge_request.scheduled_at:
            # In production, this would integrate with a job scheduler
            pass
        else:
            # Mark as sent immediately for in-app nudges
            nudge_ref.update({
                'status': NudgeStatus.SENT.value,
                'sent_at': datetime.now()
            })

        return {
            "nudge_id": nudge_id,
            "message": "Behavioral nudge created successfully",
            "personalized_message": personalized_message,
            "delivery_status": "scheduled" if nudge_request.scheduled_at else "sent",
            "target_component": nudge_request.target_score_component,
            "expires_at": behavioral_nudge.expires_at.isoformat()
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create behavioral nudge: {str(e)}"
        )

@router.get("/behavioral", response_model=List[dict])
async def get_behavioral_nudges(
    current_msme: dict = Depends(get_current_msme),
    status_filter: Optional[str] = None,
    category_filter: Optional[str] = None,
    limit: int = 20
):
    """
    Get behavioral nudges for the authenticated MSME user.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']

        # Build query
        nudges_ref = db.collection('msmes').document(msme_id).collection('behavioral_nudges')

        # Apply filters
        if status_filter:
            nudges_ref = nudges_ref.where('status', '==', status_filter)

        if category_filter:
            nudges_ref = nudges_ref.where('category', '==', category_filter)

        # Get nudges
        nudges_docs = nudges_ref.order_by('created_at', direction='DESCENDING').limit(limit).stream()

        nudges = []
        for doc in nudges_docs:
            nudge_data = doc.to_dict()
            nudges.append({
                "nudge_id": nudge_data.get('nudge_id'),
                "title": nudge_data.get('title'),
                "message": nudge_data.get('message'),
                "category": nudge_data.get('category'),
                "status": nudge_data.get('status'),
                "action_text": nudge_data.get('action_text'),
                "action_url": nudge_data.get('action_url'),
                "created_at": nudge_data.get('created_at'),
                "read_at": nudge_data.get('read_at'),
                "target_component": nudge_data.get('target_score_component'),
                "engagement_score": nudge_data.get('engagement_score')
            })

        return nudges

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get behavioral nudges: {str(e)}"
        )

@router.patch("/behavioral/{nudge_id}/read", response_model=dict)
async def mark_nudge_as_read(
    nudge_id: str,
    current_msme: dict = Depends(get_current_msme)
):
    """Mark a behavioral nudge as read"""
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']

        nudge_ref = db.collection('msmes').document(msme_id).collection('behavioral_nudges').document(nudge_id)
        nudge_doc = nudge_ref.get()

        if not nudge_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nudge not found"
            )

        # Update read status
        nudge_ref.update({
            'read_at': datetime.now(),
            'status': NudgeStatus.DELIVERED.value
        })

        return {
            "nudge_id": nudge_id,
            "message": "Nudge marked as read",
            "read_at": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to mark nudge as read: {str(e)}"
        )

# Helper functions
def _personalize_nudge_message(message: str, personalization_data: dict) -> str:
    """Personalize nudge message with MSME data"""
    try:
        return message.format(**personalization_data)
    except KeyError:
        # If formatting fails, return original message
        return message

def _generate_nudge_title(category: NudgeCategory, trigger_type: TriggerType) -> str:
    """Generate appropriate title for nudge based on category and trigger"""
    titles = {
        (NudgeCategory.GST_COMPLIANCE, TriggerType.GST_FILING_DUE): "GST Filing Reminder",
        (NudgeCategory.UPI_USAGE, TriggerType.LOW_UPI_ACTIVITY): "Boost Your Digital Payments",
        (NudgeCategory.SCORE_IMPROVEMENT, TriggerType.SCORE_DECLINE): "Improve Your Credit Score",
        (NudgeCategory.FINANCIAL_HEALTH, TriggerType.CASH_FLOW_ALERT): "Cash Flow Alert",
        (NudgeCategory.BUSINESS_GROWTH, TriggerType.GROWTH_OPPORTUNITY): "Growth Opportunity",
    }

    return titles.get((category, trigger_type), "Important Update")
