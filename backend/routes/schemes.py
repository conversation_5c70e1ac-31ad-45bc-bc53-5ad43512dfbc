"""
Scheme Navigator API Routes for MSME-facing application.

Provides endpoints for:
- Government scheme discovery and filtering
- Eligibility checking and recommendations
- Application tracking and management
- Bookmark functionality

Author: Credit Chakra Team
Version: 1.0.0
"""
from fastapi import APIRouter, HTTPException, status, Depends, Query
from typing import List, Optional, Dict, Any
from datetime import datetime, date
import uuid
import logging

from models.schemes import (
    GovernmentScheme, SchemeApplication, SchemeBookmark, EligibilityCheck,
    SchemeFilterRequest, EligibilityCheckRequest, SchemeApplicationRequest,
    SchemeRecommendation, SchemeType, ApplicationStatus
)
from routes.msme_auth import get_current_msme
from firebase.init import get_firestore_client
from utils.error_handler import handle_common_exceptions, ValidationError

logger = logging.getLogger(__name__)

router = APIRouter()

@router.get("/schemes", response_model=dict)
@handle_common_exceptions
async def get_schemes(
    scheme_type: Optional[SchemeType] = Query(None, description="Filter by scheme type"),
    business_type: Optional[str] = Query(None, description="Filter by business type"),
    location: Optional[str] = Query(None, description="Filter by location"),
    min_amount: Optional[float] = Query(None, ge=0, description="Minimum loan/grant amount"),
    max_amount: Optional[float] = Query(None, ge=0, description="Maximum loan/grant amount"),
    search: Optional[str] = Query(None, description="Search in scheme name and description"),
    page: int = Query(default=1, ge=1),
    limit: int = Query(default=20, ge=1, le=100),
    current_msme: dict = Depends(get_current_msme)
):
    """
    Get available government schemes with filtering and search.
    """
    try:
        db = get_firestore_client()
        
        # Build query
        schemes_ref = db.collection('government_schemes').where('status', '==', 'active')
        
        # Apply filters
        if scheme_type:
            schemes_ref = schemes_ref.where('scheme_type', '==', scheme_type.value)
        
        # Get schemes
        schemes_docs = schemes_ref.order_by('created_at', direction='DESCENDING').limit(limit).offset((page - 1) * limit).stream()
        
        schemes = []
        msme_id = current_msme['msme_id']
        msme_business_type = current_msme.get('business_type')
        msme_location = current_msme.get('location')
        
        for doc in schemes_docs:
            scheme_data = doc.to_dict()
            scheme_id = doc.id
            
            # Apply additional filters
            if business_type:
                eligible_types = scheme_data.get('eligibility_criteria', {}).get('business_types', [])
                if eligible_types and business_type not in eligible_types:
                    continue
            
            if location:
                eligible_locations = scheme_data.get('eligibility_criteria', {}).get('locations', [])
                if eligible_locations and location not in eligible_locations:
                    continue
            
            if min_amount and scheme_data.get('max_loan_amount', 0) < min_amount:
                continue
            
            if max_amount and scheme_data.get('max_loan_amount', float('inf')) > max_amount:
                continue
            
            if search:
                search_text = f"{scheme_data.get('name', '')} {scheme_data.get('description', '')}".lower()
                if search.lower() not in search_text:
                    continue
            
            # Check if bookmarked
            bookmark_docs = db.collection('scheme_bookmarks').where('msme_id', '==', msme_id).where('scheme_id', '==', scheme_id).limit(1).get()
            is_bookmarked = len(bookmark_docs) > 0
            
            # Check application status
            application_docs = db.collection('scheme_applications').where('msme_id', '==', msme_id).where('scheme_id', '==', scheme_id).limit(1).get()
            application_status = "not_applied"
            if application_docs:
                application_status = application_docs[0].to_dict().get('status', 'not_applied')
            
            # Quick eligibility check
            eligibility_score = await _quick_eligibility_check(scheme_data, current_msme)
            
            schemes.append({
                "scheme_id": scheme_id,
                "name": scheme_data.get('name'),
                "short_description": scheme_data.get('short_description'),
                "scheme_type": scheme_data.get('scheme_type'),
                "implementing_agency": scheme_data.get('implementing_agency'),
                "max_loan_amount": scheme_data.get('max_loan_amount'),
                "interest_rate": scheme_data.get('interest_rate'),
                "subsidy_percentage": scheme_data.get('subsidy_percentage'),
                "application_end_date": scheme_data.get('application_end_date'),
                "processing_time_days": scheme_data.get('processing_time_days'),
                "is_bookmarked": is_bookmarked,
                "application_status": application_status,
                "eligibility_score": round(eligibility_score, 1),
                "tags": scheme_data.get('tags', [])
            })
        
        # Sort by eligibility score (highest first)
        schemes.sort(key=lambda x: x['eligibility_score'], reverse=True)
        
        return {
            "schemes": schemes,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": len(schemes)  # Simplified for demo
            },
            "filters_applied": {
                "scheme_type": scheme_type.value if scheme_type else None,
                "business_type": business_type,
                "location": location,
                "min_amount": min_amount,
                "max_amount": max_amount,
                "search": search
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting schemes: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get schemes: {str(e)}"
        )

@router.get("/schemes/{scheme_id}", response_model=dict)
@handle_common_exceptions
async def get_scheme_details(
    scheme_id: str,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Get detailed information about a specific scheme.
    """
    try:
        db = get_firestore_client()
        
        # Get scheme
        scheme_doc = db.collection('government_schemes').document(scheme_id).get()
        if not scheme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Scheme not found"
            )
        
        scheme_data = scheme_doc.to_dict()
        msme_id = current_msme['msme_id']
        
        # Check if bookmarked
        bookmark_docs = db.collection('scheme_bookmarks').where('msme_id', '==', msme_id).where('scheme_id', '==', scheme_id).limit(1).get()
        is_bookmarked = len(bookmark_docs) > 0
        
        # Check application status
        application_docs = db.collection('scheme_applications').where('msme_id', '==', msme_id).where('scheme_id', '==', scheme_id).limit(1).get()
        application_status = "not_applied"
        application_data = None
        
        if application_docs:
            app_data = application_docs[0].to_dict()
            application_status = app_data.get('status', 'not_applied')
            application_data = {
                "application_id": application_docs[0].id,
                "status": application_status,
                "applied_at": app_data.get('applied_at'),
                "application_number": app_data.get('application_number'),
                "last_updated": app_data.get('last_updated')
            }
        
        # Detailed eligibility check
        eligibility_result = await _detailed_eligibility_check(scheme_data, current_msme)
        
        return {
            "scheme_id": scheme_id,
            "name": scheme_data.get('name'),
            "description": scheme_data.get('description'),
            "short_description": scheme_data.get('short_description'),
            "scheme_type": scheme_data.get('scheme_type'),
            "status": scheme_data.get('status'),
            "implementing_agency": scheme_data.get('implementing_agency'),
            "financial_details": {
                "max_loan_amount": scheme_data.get('max_loan_amount'),
                "interest_rate": scheme_data.get('interest_rate'),
                "subsidy_percentage": scheme_data.get('subsidy_percentage'),
                "processing_fee": scheme_data.get('processing_fee')
            },
            "timeline": {
                "application_start_date": scheme_data.get('application_start_date'),
                "application_end_date": scheme_data.get('application_end_date'),
                "scheme_validity": scheme_data.get('scheme_validity'),
                "processing_time_days": scheme_data.get('processing_time_days')
            },
            "eligibility_criteria": scheme_data.get('eligibility_criteria'),
            "required_documents": scheme_data.get('required_documents', []),
            "application_process": scheme_data.get('application_process', []),
            "contact_details": scheme_data.get('contact_details', {}),
            "official_website": scheme_data.get('official_website'),
            "application_link": scheme_data.get('application_link'),
            "is_bookmarked": is_bookmarked,
            "application_status": application_status,
            "application_data": application_data,
            "eligibility_check": eligibility_result,
            "analytics": {
                "total_applications": scheme_data.get('total_applications', 0),
                "success_rate": scheme_data.get('success_rate')
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting scheme details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scheme details: {str(e)}"
        )

@router.post("/schemes/{scheme_id}/check-eligibility", response_model=dict)
@handle_common_exceptions
async def check_scheme_eligibility(
    scheme_id: str,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Check detailed eligibility for a specific scheme.
    """
    try:
        db = get_firestore_client()
        
        # Get scheme
        scheme_doc = db.collection('government_schemes').document(scheme_id).get()
        if not scheme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Scheme not found"
            )
        
        scheme_data = scheme_doc.to_dict()
        
        # Perform detailed eligibility check
        eligibility_result = await _detailed_eligibility_check(scheme_data, current_msme)
        
        return {
            "scheme_id": scheme_id,
            "scheme_name": scheme_data.get('name'),
            "eligibility_check": eligibility_result,
            "checked_at": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking eligibility: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check eligibility: {str(e)}"
        )

@router.post("/schemes/{scheme_id}/bookmark", response_model=dict)
@handle_common_exceptions
async def bookmark_scheme(
    scheme_id: str,
    notes: Optional[str] = None,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Bookmark a scheme for later reference.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']
        
        # Check if already bookmarked
        existing_bookmarks = db.collection('scheme_bookmarks').where('msme_id', '==', msme_id).where('scheme_id', '==', scheme_id).limit(1).get()
        if existing_bookmarks:
            raise ValidationError("Scheme already bookmarked")
        
        # Verify scheme exists
        scheme_doc = db.collection('government_schemes').document(scheme_id).get()
        if not scheme_doc.exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Scheme not found"
            )
        
        # Create bookmark
        bookmark_id = str(uuid.uuid4())
        bookmark_data = {
            'bookmark_id': bookmark_id,
            'msme_id': msme_id,
            'scheme_id': scheme_id,
            'bookmarked_at': datetime.now(),
            'notes': notes
        }
        
        db.collection('scheme_bookmarks').document(bookmark_id).set(bookmark_data)
        
        return {
            "message": "Scheme bookmarked successfully",
            "bookmark_id": bookmark_id,
            "scheme_id": scheme_id
        }
        
    except ValidationError:
        raise
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error bookmarking scheme: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to bookmark scheme: {str(e)}"
        )

@router.delete("/schemes/{scheme_id}/bookmark", response_model=dict)
@handle_common_exceptions
async def remove_bookmark(
    scheme_id: str,
    current_msme: dict = Depends(get_current_msme)
):
    """
    Remove bookmark for a scheme.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']
        
        # Find bookmark
        bookmark_docs = db.collection('scheme_bookmarks').where('msme_id', '==', msme_id).where('scheme_id', '==', scheme_id).limit(1).get()
        if not bookmark_docs:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Bookmark not found"
            )
        
        # Delete bookmark
        db.collection('scheme_bookmarks').document(bookmark_docs[0].id).delete()
        
        return {
            "message": "Bookmark removed successfully",
            "scheme_id": scheme_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error removing bookmark: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove bookmark: {str(e)}"
        )

@router.get("/bookmarks", response_model=dict)
@handle_common_exceptions
async def get_bookmarked_schemes(
    current_msme: dict = Depends(get_current_msme)
):
    """
    Get all bookmarked schemes for the current user.
    """
    try:
        db = get_firestore_client()
        msme_id = current_msme['msme_id']
        
        # Get bookmarks
        bookmark_docs = db.collection('scheme_bookmarks').where('msme_id', '==', msme_id).order_by('bookmarked_at', direction='DESCENDING').stream()
        
        bookmarks = []
        for doc in bookmark_docs:
            bookmark_data = doc.to_dict()
            scheme_id = bookmark_data.get('scheme_id')
            
            # Get scheme details
            scheme_doc = db.collection('government_schemes').document(scheme_id).get()
            if scheme_doc.exists:
                scheme_data = scheme_doc.to_dict()
                
                bookmarks.append({
                    "bookmark_id": doc.id,
                    "scheme_id": scheme_id,
                    "scheme_name": scheme_data.get('name'),
                    "scheme_type": scheme_data.get('scheme_type'),
                    "short_description": scheme_data.get('short_description'),
                    "max_loan_amount": scheme_data.get('max_loan_amount'),
                    "bookmarked_at": bookmark_data.get('bookmarked_at'),
                    "notes": bookmark_data.get('notes')
                })
        
        return {
            "bookmarks": bookmarks,
            "total_bookmarks": len(bookmarks)
        }
        
    except Exception as e:
        logger.error(f"Error getting bookmarks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get bookmarks: {str(e)}"
        )

# Helper functions
async def _quick_eligibility_check(scheme_data: dict, msme_data: dict) -> float:
    """Quick eligibility scoring (0-100)"""
    score = 0.0
    criteria = scheme_data.get('eligibility_criteria', {})
    
    # Business type check
    eligible_types = criteria.get('business_types', [])
    if not eligible_types or msme_data.get('business_type') in eligible_types:
        score += 25
    
    # Location check
    eligible_locations = criteria.get('locations', [])
    if not eligible_locations or msme_data.get('location') in eligible_locations:
        score += 25
    
    # Turnover check (simplified)
    min_turnover = criteria.get('min_turnover')
    max_turnover = criteria.get('max_turnover')
    msme_turnover = msme_data.get('monthly_turnover', 0) * 12  # Annual
    
    if min_turnover and msme_turnover < min_turnover:
        score += 0
    elif max_turnover and msme_turnover > max_turnover:
        score += 0
    else:
        score += 25
    
    # Credit score check
    min_credit_score = criteria.get('min_credit_score')
    msme_ccr_score = msme_data.get('ccr_score', 0)
    
    if not min_credit_score or msme_ccr_score >= min_credit_score:
        score += 25
    
    return score

async def _detailed_eligibility_check(scheme_data: dict, msme_data: dict) -> dict:
    """Detailed eligibility check with recommendations"""
    criteria = scheme_data.get('eligibility_criteria', {})
    matched_criteria = []
    missing_criteria = []
    recommendations = []
    
    # Business type
    eligible_types = criteria.get('business_types', [])
    if not eligible_types or msme_data.get('business_type') in eligible_types:
        matched_criteria.append("Business type eligible")
    else:
        missing_criteria.append(f"Business type must be one of: {', '.join(eligible_types)}")
    
    # Location
    eligible_locations = criteria.get('locations', [])
    if not eligible_locations or msme_data.get('location') in eligible_locations:
        matched_criteria.append("Location eligible")
    else:
        missing_criteria.append(f"Business must be located in: {', '.join(eligible_locations)}")
    
    # Add more detailed checks here...
    
    is_eligible = len(missing_criteria) == 0
    eligibility_score = (len(matched_criteria) / (len(matched_criteria) + len(missing_criteria)) * 100) if (matched_criteria or missing_criteria) else 0
    
    return {
        "is_eligible": is_eligible,
        "eligibility_score": round(eligibility_score, 1),
        "matched_criteria": matched_criteria,
        "missing_criteria": missing_criteria,
        "recommendations": recommendations
    }
