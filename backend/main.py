"""
Credit Chakra FastAPI Application.

This is the main entry point for the Credit Chakra MSME credit scoring and monitoring platform.
It provides RESTful APIs for portfolio management, risk assessment, and analytics.

Author: Credit Chakra Team
Version: 1.0.0
"""
import logging
import os
import time

from dotenv import load_dotenv
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('credit_chakra.log') if not os.getenv("CLOUD_RUN") else logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize demo data
try:
    from init_demo_data import init_demo_data
    init_demo_data()
except Exception as e:
    logger.warning(f"Could not initialize demo data: {e}")

# Initialize FastAPI app
app = FastAPI(
    title="Credit Chakra API",
    description="MSME Credit Scoring and Monitoring Platform",
    version="1.0.0",
    docs_url="/docs" if os.getenv("DEBUG", "False").lower() == "true" else None,
    redoc_url="/redoc" if os.getenv("DEBUG", "False").lower() == "true" else None
)

# Add security middleware
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", "testserver", "*.creditchakra.com"]
)

# Add performance monitoring and security headers middleware
@app.middleware("http")
async def add_security_and_performance_headers(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    # Performance headers
    response.headers["X-Process-Time"] = str(process_time)

    # Security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"

    # Cache control for API responses
    if request.url.path.startswith("/api/"):
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"

    # Log slow requests (>2 seconds)
    if process_time > 2.0:
        logger.warning(f"Slow request: {request.method} {request.url} took {process_time:.2f}s")

    return response

# Configure CORS properly for production
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:3001").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization", "Accept"],
)

# Import routers
from routes import (
    msme, signals, nudges, dashboard, data_refresh, test_router, copilot,
    account_aggregator, cash_flow, data_integration, financial_metrics, gst, reports,
    ccr_score, msme_auth, training, schemes
)

# Include routers
app.include_router(test_router.router, prefix="/test", tags=["Test"])
app.include_router(msme.router, prefix="/msme", tags=["MSME"])
app.include_router(signals.router, prefix="/api/signals", tags=["Signals"])
app.include_router(nudges.router, prefix="/nudges", tags=["Nudges"])
app.include_router(dashboard.router, prefix="/api/dashboard", tags=["Dashboard"])
app.include_router(data_refresh.router, prefix="/api", tags=["Data Refresh"])
app.include_router(copilot.router, prefix="/api/copilot", tags=["AI Copilot"])
app.include_router(account_aggregator.router, prefix="/api", tags=["Account Aggregator"])
app.include_router(cash_flow.router, prefix="/api", tags=["Cash Flow"])
app.include_router(data_integration.router, prefix="/api", tags=["Data Integration"])
app.include_router(financial_metrics.router, prefix="/api", tags=["Financial Metrics"])
app.include_router(gst.router, prefix="/api", tags=["GST"])
app.include_router(reports.router, prefix="/api/reports", tags=["Reports"])
app.include_router(ccr_score.router, prefix="/api/ccr", tags=["CCR Score"])
# MSME-facing application routes
app.include_router(msme_auth.router, prefix="/api/auth", tags=["MSME Authentication"])
app.include_router(training.router, prefix="/api/training", tags=["Training & Education"])
app.include_router(schemes.router, prefix="/api/schemes", tags=["Scheme Navigator"])

@app.get("/", tags=["System"])
async def root():
    """
    Root endpoint providing basic API information.

    Returns:
        dict: Basic API status message
    """
    return {"message": "Credit Chakra API is running"}

@app.get("/health", tags=["System"])
async def health_check():
    """
    Health check endpoint for monitoring and load balancers.

    Returns:
        dict: Service health status and identification
    """
    return {"status": "healthy", "service": "credit-chakra-backend"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
