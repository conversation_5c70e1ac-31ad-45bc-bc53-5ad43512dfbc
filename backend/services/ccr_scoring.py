"""
CCR (Credit Chakra Rating) Scoring Service for MSME-facing application.

This service provides enhanced scoring with 5 subcomponents:
1. GST Compliance (0-100)
2. UPI Diversity (0-100) 
3. Digital Presence (0-100)
4. Financial Health (0-100)
5. Business Stability (0-100)

Author: Credit Chakra Team
Version: 1.0.0
"""
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging

from firebase.init import get_firestore_client
from models.msme import CCRScoreBreakdown, CCRTrend, RiskBand
from models.signal import SignalSource
from services.scoring import normalize_signal, SIGNAL_WEIGHTS

logger = logging.getLogger(__name__)

class CCRScoringService:
    """Enhanced scoring service for MSME-facing CCR scores"""
    
    def __init__(self):
        self.db = get_firestore_client()
        
        # CCR component weights (must sum to 1.0)
        self.component_weights = {
            'gst_compliance': 0.25,
            'upi_diversity': 0.20,
            'digital_presence': 0.20,
            'financial_health': 0.20,
            'business_stability': 0.15
        }
    
    async def calculate_ccr_score(self, msme_id: str) -> <PERSON><PERSON>[float, CCRScoreBreakdown]:
        """
        Calculate comprehensive CCR score with breakdown.
        
        Args:
            msme_id: MSME identifier
            
        Returns:
            Tuple of (overall_score, breakdown)
        """
        try:
            # Get all signals for the MSME
            signals = await self._get_msme_signals(msme_id)
            
            # Calculate each component
            gst_score = await self._calculate_gst_compliance(signals)
            upi_score = await self._calculate_upi_diversity(signals)
            digital_score = await self._calculate_digital_presence(signals)
            financial_score = await self._calculate_financial_health(signals)
            stability_score = await self._calculate_business_stability(signals)
            
            # Create breakdown
            breakdown = CCRScoreBreakdown(
                gst_compliance=gst_score,
                upi_diversity=upi_score,
                digital_presence=digital_score,
                financial_health=financial_score,
                business_stability=stability_score
            )
            
            # Calculate weighted overall score
            overall_score = (
                gst_score * self.component_weights['gst_compliance'] +
                upi_score * self.component_weights['upi_diversity'] +
                digital_score * self.component_weights['digital_presence'] +
                financial_score * self.component_weights['financial_health'] +
                stability_score * self.component_weights['business_stability']
            )
            
            return overall_score, breakdown
            
        except Exception as e:
            logger.error(f"Error calculating CCR score for MSME {msme_id}: {e}")
            # Return default scores on error
            default_breakdown = CCRScoreBreakdown()
            return 0.0, default_breakdown
    
    async def _get_msme_signals(self, msme_id: str) -> List[Dict]:
        """Get all signals for an MSME"""
        try:
            signals_ref = self.db.collection('msmes').document(msme_id).collection('signals')
            signals_docs = signals_ref.order_by('timestamp', direction='DESCENDING').limit(100).stream()
            
            signals = []
            for doc in signals_docs:
                signal_data = doc.to_dict()
                signal_data['signal_id'] = doc.id
                signals.append(signal_data)
            
            return signals
        except Exception as e:
            logger.error(f"Error fetching signals for MSME {msme_id}: {e}")
            return []
    
    async def _calculate_gst_compliance(self, signals: List[Dict]) -> float:
        """Calculate GST compliance score (0-100)"""
        try:
            gst_signals = [s for s in signals if s.get('source') == 'gst']
            
            if not gst_signals:
                return 0.0
            
            # Get recent GST filings (last 6 months)
            recent_signals = self._filter_recent_signals(gst_signals, months=6)
            
            if not recent_signals:
                return 20.0  # Some credit for having GST registration
            
            # Calculate compliance factors
            filing_regularity = self._calculate_filing_regularity(recent_signals)
            turnover_consistency = self._calculate_turnover_consistency(recent_signals)
            growth_trend = self._calculate_growth_trend(recent_signals)
            
            # Weighted score
            compliance_score = (
                filing_regularity * 0.4 +
                turnover_consistency * 0.35 +
                growth_trend * 0.25
            ) * 100
            
            return min(100.0, max(0.0, compliance_score))
            
        except Exception as e:
            logger.error(f"Error calculating GST compliance: {e}")
            return 0.0
    
    async def _calculate_upi_diversity(self, signals: List[Dict]) -> float:
        """Calculate UPI transaction diversity score (0-100)"""
        try:
            upi_signals = [s for s in signals if s.get('source') == 'upi']
            
            if not upi_signals:
                return 0.0
            
            recent_signals = self._filter_recent_signals(upi_signals, months=3)
            
            if not recent_signals:
                return 0.0
            
            # Calculate UPI metrics
            merchant_diversity = self._calculate_merchant_diversity(recent_signals)
            transaction_volume = self._calculate_transaction_volume(recent_signals)
            frequency_score = self._calculate_transaction_frequency(recent_signals)
            
            # Weighted UPI score
            upi_score = (
                merchant_diversity * 0.4 +
                transaction_volume * 0.35 +
                frequency_score * 0.25
            ) * 100
            
            return min(100.0, max(0.0, upi_score))
            
        except Exception as e:
            logger.error(f"Error calculating UPI diversity: {e}")
            return 0.0
    
    async def _calculate_digital_presence(self, signals: List[Dict]) -> float:
        """Calculate digital presence score (0-100)"""
        try:
            digital_sources = ['reviews', 'instagram', 'maps', 'justdial']
            digital_signals = [s for s in signals if s.get('source') in digital_sources]
            
            if not digital_signals:
                return 0.0
            
            # Calculate digital metrics
            review_score = self._calculate_review_score(digital_signals)
            social_presence = self._calculate_social_presence(digital_signals)
            online_visibility = self._calculate_online_visibility(digital_signals)
            
            # Weighted digital score
            digital_score = (
                review_score * 0.5 +
                social_presence * 0.3 +
                online_visibility * 0.2
            ) * 100
            
            return min(100.0, max(0.0, digital_score))
            
        except Exception as e:
            logger.error(f"Error calculating digital presence: {e}")
            return 0.0
    
    async def _calculate_financial_health(self, signals: List[Dict]) -> float:
        """Calculate financial health score (0-100)"""
        try:
            # Use GST turnover as primary financial indicator
            gst_signals = [s for s in signals if s.get('source') == 'gst']
            
            if not gst_signals:
                return 0.0
            
            recent_signals = self._filter_recent_signals(gst_signals, months=12)
            
            if len(recent_signals) < 3:
                return 20.0  # Minimal score for limited data
            
            # Calculate financial metrics
            revenue_stability = self._calculate_revenue_stability(recent_signals)
            growth_momentum = self._calculate_growth_momentum(recent_signals)
            scale_score = self._calculate_business_scale(recent_signals)
            
            # Weighted financial score
            financial_score = (
                revenue_stability * 0.4 +
                growth_momentum * 0.35 +
                scale_score * 0.25
            ) * 100
            
            return min(100.0, max(0.0, financial_score))
            
        except Exception as e:
            logger.error(f"Error calculating financial health: {e}")
            return 0.0
    
    async def _calculate_business_stability(self, signals: List[Dict]) -> float:
        """Calculate business stability score (0-100)"""
        try:
            # Consider all signal sources for stability assessment
            if not signals:
                return 0.0
            
            # Calculate stability factors
            data_consistency = self._calculate_data_consistency(signals)
            longevity_score = self._calculate_business_longevity(signals)
            signal_diversity = self._calculate_signal_diversity(signals)
            
            # Weighted stability score
            stability_score = (
                data_consistency * 0.4 +
                longevity_score * 0.35 +
                signal_diversity * 0.25
            ) * 100
            
            return min(100.0, max(0.0, stability_score))
            
        except Exception as e:
            logger.error(f"Error calculating business stability: {e}")
            return 0.0
    
    def _filter_recent_signals(self, signals: List[Dict], months: int) -> List[Dict]:
        """Filter signals to recent timeframe"""
        cutoff_date = datetime.now() - timedelta(days=months * 30)
        recent_signals = []
        
        for signal in signals:
            timestamp = signal.get('timestamp')
            if timestamp and timestamp > cutoff_date:
                recent_signals.append(signal)
        
        return recent_signals
    
    def _calculate_filing_regularity(self, gst_signals: List[Dict]) -> float:
        """Calculate GST filing regularity (0-1)"""
        if len(gst_signals) < 3:
            return 0.3
        
        # Check for consistent monthly filings
        months_with_filings = len(set(s.get('metadata', {}).get('month', '') for s in gst_signals))
        expected_months = min(6, len(gst_signals))
        
        return min(1.0, months_with_filings / expected_months)
    
    def _calculate_turnover_consistency(self, gst_signals: List[Dict]) -> float:
        """Calculate turnover consistency (0-1)"""
        if len(gst_signals) < 2:
            return 0.5
        
        turnovers = [s.get('value', 0) for s in gst_signals if s.get('value', 0) > 0]
        
        if len(turnovers) < 2:
            return 0.3
        
        # Calculate coefficient of variation (lower is better)
        mean_turnover = sum(turnovers) / len(turnovers)
        variance = sum((x - mean_turnover) ** 2 for x in turnovers) / len(turnovers)
        std_dev = variance ** 0.5
        
        if mean_turnover == 0:
            return 0.0
        
        cv = std_dev / mean_turnover
        # Convert to score (lower CV = higher score)
        consistency_score = max(0.0, 1.0 - cv)
        
        return min(1.0, consistency_score)
    
    def _calculate_growth_trend(self, gst_signals: List[Dict]) -> float:
        """Calculate growth trend (0-1)"""
        if len(gst_signals) < 3:
            return 0.5
        
        # Sort by timestamp
        sorted_signals = sorted(gst_signals, key=lambda x: x.get('timestamp', datetime.min))
        turnovers = [s.get('value', 0) for s in sorted_signals if s.get('value', 0) > 0]
        
        if len(turnovers) < 3:
            return 0.5
        
        # Calculate simple growth rate
        recent_avg = sum(turnovers[-3:]) / 3
        older_avg = sum(turnovers[:3]) / 3
        
        if older_avg == 0:
            return 0.5
        
        growth_rate = (recent_avg - older_avg) / older_avg
        
        # Convert to score (positive growth = higher score)
        if growth_rate > 0.2:  # >20% growth
            return 1.0
        elif growth_rate > 0:
            return 0.5 + (growth_rate / 0.4)  # Scale 0-20% to 0.5-1.0
        else:
            return max(0.0, 0.5 + growth_rate)  # Negative growth reduces score
    
    # Additional helper methods would be implemented here for other calculations
    # (merchant_diversity, transaction_volume, review_score, etc.)
    # These follow similar patterns to the above methods
    
    def _calculate_merchant_diversity(self, upi_signals: List[Dict]) -> float:
        """Calculate UPI merchant diversity (0-1)"""
        # Implementation for merchant diversity calculation
        return 0.5  # Placeholder
    
    def _calculate_transaction_volume(self, upi_signals: List[Dict]) -> float:
        """Calculate transaction volume score (0-1)"""
        # Implementation for transaction volume calculation
        return 0.5  # Placeholder
    
    def _calculate_transaction_frequency(self, upi_signals: List[Dict]) -> float:
        """Calculate transaction frequency score (0-1)"""
        # Implementation for transaction frequency calculation
        return 0.5  # Placeholder
    
    def _calculate_review_score(self, digital_signals: List[Dict]) -> float:
        """Calculate review score (0-1)"""
        # Implementation for review score calculation
        return 0.5  # Placeholder
    
    def _calculate_social_presence(self, digital_signals: List[Dict]) -> float:
        """Calculate social media presence (0-1)"""
        # Implementation for social presence calculation
        return 0.5  # Placeholder
    
    def _calculate_online_visibility(self, digital_signals: List[Dict]) -> float:
        """Calculate online visibility (0-1)"""
        # Implementation for online visibility calculation
        return 0.5  # Placeholder
    
    def _calculate_revenue_stability(self, gst_signals: List[Dict]) -> float:
        """Calculate revenue stability (0-1)"""
        # Implementation for revenue stability calculation
        return 0.5  # Placeholder
    
    def _calculate_growth_momentum(self, gst_signals: List[Dict]) -> float:
        """Calculate growth momentum (0-1)"""
        # Implementation for growth momentum calculation
        return 0.5  # Placeholder
    
    def _calculate_business_scale(self, gst_signals: List[Dict]) -> float:
        """Calculate business scale score (0-1)"""
        # Implementation for business scale calculation
        return 0.5  # Placeholder
    
    def _calculate_data_consistency(self, signals: List[Dict]) -> float:
        """Calculate data consistency across sources (0-1)"""
        # Implementation for data consistency calculation
        return 0.5  # Placeholder
    
    def _calculate_business_longevity(self, signals: List[Dict]) -> float:
        """Calculate business longevity score (0-1)"""
        # Implementation for business longevity calculation
        return 0.5  # Placeholder
    
    def _calculate_signal_diversity(self, signals: List[Dict]) -> float:
        """Calculate signal source diversity (0-1)"""
        # Implementation for signal diversity calculation
        return 0.5  # Placeholder

# Global instance
ccr_scoring_service = CCRScoringService()
