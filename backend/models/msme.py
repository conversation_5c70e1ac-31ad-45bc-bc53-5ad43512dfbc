from pydantic import BaseModel, Field
from typing import List, Optional, Dict
from datetime import datetime
from enum import Enum

class RiskBand(str, Enum):
    GREEN = "green"
    YELLOW = "yellow"
    RED = "red"

class BusinessType(str, Enum):
    RETAIL = "retail"
    B2B = "b2b"
    MANUFACTURING = "manufacturing"
    SERVICES = "services"

class CCRScoreBreakdown(BaseModel):
    """CCR (Credit Chakra Rating) Score Breakdown with 5 subcomponents"""
    gst_compliance: float = Field(0.0, ge=0, le=100, description="GST compliance and filing regularity")
    upi_diversity: float = Field(0.0, ge=0, le=100, description="UPI transaction diversity and volume")
    digital_presence: float = Field(0.0, ge=0, le=100, description="Online presence and customer reviews")
    financial_health: float = Field(0.0, ge=0, le=100, description="Financial stability and cash flow")
    business_stability: float = Field(0.0, ge=0, le=100, description="Business longevity and growth trends")

    @property
    def overall_score(self) -> float:
        """Calculate overall CCR score (0-100) from subcomponents"""
        return (self.gst_compliance + self.upi_diversity + self.digital_presence +
                self.financial_health + self.business_stability) / 5

class CCRTrend(BaseModel):
    """CCR Score trend data"""
    date: datetime
    score: float = Field(ge=0, le=100)
    breakdown: CCRScoreBreakdown

class MSMEProfile(BaseModel):
    msme_id: Optional[str] = None
    name: str = Field(..., min_length=1, max_length=200)
    business_type: BusinessType
    location: str = Field(..., min_length=1, max_length=100)
    created_at: Optional[datetime] = None
    score: Optional[float] = Field(None, ge=0, le=1000)  # Legacy score for backward compatibility
    risk_band: Optional[RiskBand] = None
    tags: List[str] = Field(default_factory=list)

    # Enhanced fields for banking/financial industry standards
    gst_number: Optional[str] = None
    gst_compliance: Optional[float] = Field(None, ge=0, le=100)
    banking_health: Optional[float] = Field(None, ge=0, le=100)
    monthly_turnover: Optional[float] = Field(None, ge=0)
    digital_score: Optional[float] = Field(None, ge=0, le=100)

    # CCR Score fields for MSME-facing application
    ccr_score: Optional[float] = Field(None, ge=0, le=100, description="Overall CCR score (0-100)")
    ccr_breakdown: Optional[CCRScoreBreakdown] = None
    ccr_trends: List[CCRTrend] = Field(default_factory=list)
    last_ccr_update: Optional[datetime] = None

    # MSME user authentication fields
    email: Optional[str] = None
    phone: Optional[str] = None
    is_verified: bool = Field(default=False)
    verification_documents: List[str] = Field(default_factory=list)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        extra = "ignore"  # Ignore extra fields from Firestore

class MSMECreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    business_type: BusinessType
    location: str = Field(..., min_length=1, max_length=100)
    tags: List[str] = Field(default_factory=list)

class MSMEUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    business_type: Optional[BusinessType] = None
    location: Optional[str] = Field(None, min_length=1, max_length=100)
    tags: Optional[List[str]] = None

    # Enhanced fields for banking/financial industry standards
    gst_number: Optional[str] = None
    gst_compliance: Optional[float] = Field(None, ge=0, le=100)
    banking_health: Optional[float] = Field(None, ge=0, le=100)
    monthly_turnover: Optional[float] = Field(None, ge=0)
    digital_score: Optional[float] = Field(None, ge=0, le=100)
