from pydantic import BaseModel, Field
from typing import Optional, Literal, List, Dict, Any
from datetime import datetime
from enum import Enum

class TriggerType(str, Enum):
    SCORE_DROP = "score_drop"
    MANUAL = "manual"
    RISK_ALERT = "risk_alert"
    # New behavioral triggers for MSME-facing app
    GST_FILING_REMINDER = "gst_filing_reminder"
    UPI_USAGE_ENCOURAGEMENT = "upi_usage_encouragement"
    TRAINING_RECOMMENDATION = "training_recommendation"
    SCHEME_OPPORTUNITY = "scheme_opportunity"
    SCORE_IMPROVEMENT_TIP = "score_improvement_tip"
    MILESTONE_CELEBRATION = "milestone_celebration"
    INACTIVITY_REMINDER = "inactivity_reminder"

class Medium(str, Enum):
    WHATSAPP = "whatsapp"
    EMAIL = "email"
    SMS = "sms"
    PUSH_NOTIFICATION = "push_notification"
    IN_APP = "in_app"

class NudgeStatus(str, Enum):
    SENT = "sent"
    DELIVERED = "delivered"
    FAILED = "failed"
    PENDING = "pending"
    SCHEDULED = "scheduled"

class NudgePriority(str, Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class NudgeCategory(str, Enum):
    COMPLIANCE = "compliance"
    FINANCIAL = "financial"
    GROWTH = "growth"
    EDUCATION = "education"
    OPPORTUNITY = "opportunity"
    REMINDER = "reminder"

class NudgeRequest(BaseModel):
    trigger_type: Literal["score_drop", "manual", "risk_alert"]
    message: str = Field(..., min_length=1, max_length=1000)
    medium: Literal["whatsapp", "email", "sms"]
    metadata: Optional[dict] = Field(default_factory=dict)

class Nudge(BaseModel):
    nudge_id: str
    msme_id: str
    trigger_type: str
    message: str
    medium: str
    sent_at: datetime
    status: Literal["sent", "delivered", "failed"] = "sent"
    metadata: dict = Field(default_factory=dict)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Enhanced nudge models for MSME-facing application
class BehavioralNudge(BaseModel):
    """Enhanced nudge model with behavioral triggers and personalization"""
    nudge_id: Optional[str] = None
    msme_id: str
    trigger_type: TriggerType
    category: NudgeCategory
    priority: NudgePriority = NudgePriority.MEDIUM

    # Message content
    title: str = Field(..., min_length=1, max_length=100)
    message: str = Field(..., min_length=1, max_length=1000)
    action_text: Optional[str] = Field(None, max_length=50)
    action_url: Optional[str] = None

    # Delivery
    medium: Medium
    scheduled_at: Optional[datetime] = None
    sent_at: Optional[datetime] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None
    clicked_at: Optional[datetime] = None

    # Status and tracking
    status: NudgeStatus = NudgeStatus.PENDING
    delivery_attempts: int = Field(default=0, ge=0)

    # Personalization data
    personalization_data: Dict[str, Any] = Field(default_factory=dict)
    target_score_component: Optional[str] = None  # Which CCR component to improve

    # Behavioral context
    user_context: Dict[str, Any] = Field(default_factory=dict)
    trigger_conditions: Dict[str, Any] = Field(default_factory=dict)

    # Effectiveness tracking
    engagement_score: Optional[float] = Field(None, ge=0, le=100)
    conversion_tracked: bool = Field(default=False)

    # Metadata
    created_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class NudgeTemplate(BaseModel):
    """Template for generating personalized nudges"""
    template_id: Optional[str] = None
    name: str = Field(..., min_length=1, max_length=100)
    trigger_type: TriggerType
    category: NudgeCategory

    # Template content
    title_template: str = Field(..., description="Title with placeholders like {business_name}")
    message_template: str = Field(..., description="Message with placeholders")
    action_text_template: Optional[str] = None

    # Targeting
    target_business_types: List[str] = Field(default_factory=list)
    target_score_ranges: List[Dict[str, float]] = Field(default_factory=list)
    target_risk_bands: List[str] = Field(default_factory=list)

    # Timing
    optimal_send_times: List[str] = Field(default_factory=list)  # Hours like "09:00", "14:00"
    frequency_limit: Optional[int] = Field(None, description="Max times per month")

    # Effectiveness
    historical_engagement_rate: Optional[float] = Field(None, ge=0, le=100)
    is_active: bool = Field(default=True)

    class Config:
        use_enum_values = True

class NudgePreferences(BaseModel):
    """User preferences for nudge delivery"""
    msme_id: str

    # Channel preferences
    preferred_medium: List[Medium] = Field(default_factory=list)
    blocked_medium: List[Medium] = Field(default_factory=list)

    # Timing preferences
    preferred_hours: List[str] = Field(default_factory=list)  # "09:00", "14:00", etc.
    timezone: str = Field(default="Asia/Kolkata")

    # Content preferences
    enabled_categories: List[NudgeCategory] = Field(default_factory=list)
    disabled_categories: List[NudgeCategory] = Field(default_factory=list)

    # Frequency control
    max_nudges_per_day: int = Field(default=3, ge=0, le=10)
    max_nudges_per_week: int = Field(default=10, ge=0, le=50)

    # Contact details
    whatsapp_number: Optional[str] = None
    email_address: Optional[str] = None

    # Preferences metadata
    updated_at: Optional[datetime] = None

    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Keep existing models for backward compatibility
class NudgeCreate(BaseModel):
    msme_id: str = Field(..., min_length=1)
    trigger_type: TriggerType
    message: str = Field(..., min_length=1, max_length=1000)
    medium: Medium
    metadata: Optional[dict] = Field(default_factory=dict)

class NudgeUpdate(BaseModel):
    message: Optional[str] = Field(None, min_length=1, max_length=1000)
    status: Optional[NudgeStatus] = None
    metadata: Optional[dict] = None

# New request/response models for MSME app
class NudgeActionRequest(BaseModel):
    """Request when user takes action on a nudge"""
    nudge_id: str
    action_type: str = Field(..., description="clicked, dismissed, completed, etc.")
    action_data: Optional[Dict[str, Any]] = Field(default_factory=dict)

class NudgePreferencesUpdate(BaseModel):
    """Request to update nudge preferences"""
    preferred_medium: Optional[List[Medium]] = None
    preferred_hours: Optional[List[str]] = None
    enabled_categories: Optional[List[NudgeCategory]] = None
    max_nudges_per_day: Optional[int] = Field(None, ge=0, le=10)
    whatsapp_number: Optional[str] = None
    email_address: Optional[str] = None
