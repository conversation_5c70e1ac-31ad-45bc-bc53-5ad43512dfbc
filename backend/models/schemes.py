from pydantic import BaseModel, Field, HttpUrl
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from enum import Enum

class SchemeType(str, Enum):
    LOAN = "loan"
    SUBSIDY = "subsidy"
    GRANT = "grant"
    TAX_BENEFIT = "tax_benefit"
    TRAINING = "training"
    INFRASTRUCTURE = "infrastructure"
    TECHNOLOGY = "technology"
    EXPORT_PROMOTION = "export_promotion"

class SchemeStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    UPCOMING = "upcoming"
    EXPIRED = "expired"

class ApplicationStatus(str, Enum):
    NOT_APPLIED = "not_applied"
    DRAFT = "draft"
    SUBMITTED = "submitted"
    UNDER_REVIEW = "under_review"
    APPROVED = "approved"
    REJECTED = "rejected"
    DISBURSED = "disbursed"

class EligibilityCriteria(BaseModel):
    """Eligibility criteria for schemes"""
    min_turnover: Optional[float] = Field(None, ge=0)
    max_turnover: Optional[float] = Field(None, ge=0)
    business_types: List[str] = Field(default_factory=list)
    locations: List[str] = Field(default_factory=list)  # States/districts
    sectors: List[str] = Field(default_factory=list)
    min_years_operation: Optional[int] = Field(None, ge=0)
    max_years_operation: Optional[int] = Field(None, ge=0)
    
    # Financial criteria
    min_credit_score: Optional[float] = Field(None, ge=0, le=100)
    max_existing_loans: Optional[float] = Field(None, ge=0)
    
    # Special criteria
    women_entrepreneur: Optional[bool] = None
    sc_st_category: Optional[bool] = None
    minority_community: Optional[bool] = None
    first_generation_entrepreneur: Optional[bool] = None
    
    # Custom criteria
    custom_criteria: Dict[str, Any] = Field(default_factory=dict)

class GovernmentScheme(BaseModel):
    """Government scheme model for MSME benefits"""
    scheme_id: Optional[str] = None
    name: str = Field(..., min_length=1, max_length=300)
    description: str = Field(..., max_length=2000)
    short_description: str = Field(..., max_length=500)
    
    # Scheme details
    scheme_type: SchemeType
    status: SchemeStatus = SchemeStatus.ACTIVE
    implementing_agency: str = Field(..., description="Government department/agency")
    
    # Financial details
    max_loan_amount: Optional[float] = Field(None, ge=0)
    interest_rate: Optional[float] = Field(None, ge=0, le=100)
    subsidy_percentage: Optional[float] = Field(None, ge=0, le=100)
    processing_fee: Optional[float] = Field(None, ge=0)
    
    # Timeline
    application_start_date: Optional[date] = None
    application_end_date: Optional[date] = None
    scheme_validity: Optional[date] = None
    processing_time_days: Optional[int] = Field(None, gt=0)
    
    # Eligibility
    eligibility_criteria: EligibilityCriteria
    
    # Documentation
    required_documents: List[str] = Field(default_factory=list)
    application_process: List[str] = Field(default_factory=list)
    
    # Contact and links
    official_website: Optional[HttpUrl] = None
    contact_details: Dict[str, str] = Field(default_factory=dict)
    application_link: Optional[HttpUrl] = None
    
    # Metadata
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    tags: List[str] = Field(default_factory=list)
    
    # Analytics
    total_applications: int = Field(default=0, ge=0)
    success_rate: Optional[float] = Field(None, ge=0, le=100)
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat()
        }

class SchemeApplication(BaseModel):
    """MSME application for a government scheme"""
    application_id: Optional[str] = None
    msme_id: str
    scheme_id: str
    
    # Application details
    status: ApplicationStatus = ApplicationStatus.NOT_APPLIED
    applied_at: Optional[datetime] = None
    last_updated: Optional[datetime] = None
    
    # Application data
    application_data: Dict[str, Any] = Field(default_factory=dict)
    submitted_documents: List[str] = Field(default_factory=list)
    
    # Processing
    application_number: Optional[str] = None
    processing_officer: Optional[str] = None
    review_comments: List[str] = Field(default_factory=list)
    
    # Outcome
    approved_amount: Optional[float] = Field(None, ge=0)
    disbursed_amount: Optional[float] = Field(None, ge=0)
    rejection_reason: Optional[str] = None
    
    # Timeline tracking
    status_history: List[Dict[str, Any]] = Field(default_factory=list)
    expected_completion_date: Optional[date] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat()
        }

class SchemeBookmark(BaseModel):
    """User bookmarks for schemes"""
    bookmark_id: Optional[str] = None
    msme_id: str
    scheme_id: str
    bookmarked_at: datetime
    notes: Optional[str] = Field(None, max_length=500)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class EligibilityCheck(BaseModel):
    """Eligibility check result"""
    scheme_id: str
    is_eligible: bool
    eligibility_score: float = Field(..., ge=0, le=100)
    matched_criteria: List[str] = Field(default_factory=list)
    missing_criteria: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)

# Request/Response models
class SchemeFilterRequest(BaseModel):
    """Request model for filtering schemes"""
    scheme_types: Optional[List[SchemeType]] = None
    business_types: Optional[List[str]] = None
    locations: Optional[List[str]] = None
    sectors: Optional[List[str]] = None
    min_amount: Optional[float] = Field(None, ge=0)
    max_amount: Optional[float] = Field(None, ge=0)
    max_interest_rate: Optional[float] = Field(None, ge=0, le=100)
    
    # Search
    search_query: Optional[str] = Field(None, max_length=200)
    
    # Pagination
    page: int = Field(default=1, ge=1)
    limit: int = Field(default=20, ge=1, le=100)

class EligibilityCheckRequest(BaseModel):
    """Request model for checking scheme eligibility"""
    msme_id: str
    scheme_ids: List[str] = Field(..., min_items=1)

class SchemeApplicationRequest(BaseModel):
    """Request model for scheme application"""
    scheme_id: str
    application_data: Dict[str, Any]
    documents: List[str] = Field(default_factory=list)

class SchemeRecommendation(BaseModel):
    """Recommended scheme for MSME"""
    scheme: GovernmentScheme
    eligibility_check: EligibilityCheck
    recommendation_score: float = Field(..., ge=0, le=100)
    recommendation_reason: str
    priority: str = Field(..., description="high, medium, low")

class SchemeAnalytics(BaseModel):
    """Analytics for scheme performance"""
    total_schemes: int
    active_schemes: int
    total_applications: int
    success_rate: float = Field(..., ge=0, le=100)
    popular_schemes: List[Dict[str, Any]] = Field(default_factory=list)
    category_distribution: Dict[str, int] = Field(default_factory=dict)
