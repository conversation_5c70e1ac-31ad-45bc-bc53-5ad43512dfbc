from pydantic import BaseModel, Field, HttpUrl
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum

class ContentType(str, Enum):
    VIDEO = "video"
    ARTICLE = "article"
    QUIZ = "quiz"
    SCORM = "scorm"
    PDF = "pdf"
    INTERACTIVE = "interactive"

class DifficultyLevel(str, Enum):
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"

class CourseStatus(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CERTIFIED = "certified"

class QuizQuestionType(str, Enum):
    MULTIPLE_CHOICE = "multiple_choice"
    TRUE_FALSE = "true_false"
    FILL_BLANK = "fill_blank"
    ESSAY = "essay"

class TrainingCourse(BaseModel):
    """Training course model for MSME education"""
    course_id: Optional[str] = None
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., max_length=1000)
    category: str = Field(..., description="e.g., 'GST Compliance', 'Digital Marketing', 'Financial Management'")
    difficulty_level: DifficultyLevel
    estimated_duration_minutes: int = Field(..., gt=0)
    
    # Content structure
    modules: List[str] = Field(default_factory=list, description="List of module IDs")
    prerequisites: List[str] = Field(default_factory=list, description="Required course IDs")
    
    # Metadata
    created_by: str = Field(..., description="Instructor or content creator")
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    is_active: bool = Field(default=True)
    
    # Certification
    has_certificate: bool = Field(default=False)
    certificate_template_id: Optional[str] = None
    passing_score: Optional[float] = Field(None, ge=0, le=100)
    
    # Tags and filtering
    tags: List[str] = Field(default_factory=list)
    target_business_types: List[str] = Field(default_factory=list)
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class TrainingModule(BaseModel):
    """Individual training module within a course"""
    module_id: Optional[str] = None
    course_id: str
    title: str = Field(..., min_length=1, max_length=200)
    description: str = Field(..., max_length=500)
    order_index: int = Field(..., ge=0)
    
    # Content
    content_type: ContentType
    content_url: Optional[HttpUrl] = None
    content_data: Optional[Dict[str, Any]] = Field(default_factory=dict)
    duration_minutes: int = Field(..., gt=0)
    
    # SCORM specific fields
    scorm_package_url: Optional[HttpUrl] = None
    scorm_version: Optional[str] = None
    
    # Interactive content
    interactive_elements: List[Dict[str, Any]] = Field(default_factory=list)
    
    # Completion criteria
    completion_criteria: Dict[str, Any] = Field(default_factory=dict)
    is_mandatory: bool = Field(default=True)
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class QuizQuestion(BaseModel):
    """Quiz question model"""
    question_id: Optional[str] = None
    module_id: str
    question_text: str = Field(..., min_length=1, max_length=1000)
    question_type: QuizQuestionType
    
    # Multiple choice options
    options: List[str] = Field(default_factory=list)
    correct_answer: str = Field(..., description="Correct answer or answer key")
    
    # Scoring
    points: float = Field(default=1.0, gt=0)
    explanation: Optional[str] = Field(None, max_length=500)
    
    # Metadata
    difficulty: DifficultyLevel = DifficultyLevel.INTERMEDIATE
    tags: List[str] = Field(default_factory=list)

class UserCourseProgress(BaseModel):
    """Track user progress through courses"""
    progress_id: Optional[str] = None
    msme_id: str
    course_id: str
    
    # Progress tracking
    status: CourseStatus = CourseStatus.NOT_STARTED
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    last_accessed: Optional[datetime] = None
    
    # Module progress
    completed_modules: List[str] = Field(default_factory=list)
    current_module_id: Optional[str] = None
    
    # Quiz and assessment
    quiz_attempts: List[Dict[str, Any]] = Field(default_factory=list)
    best_score: Optional[float] = Field(None, ge=0, le=100)
    total_time_spent_minutes: int = Field(default=0, ge=0)
    
    # Certification
    certificate_issued: bool = Field(default=False)
    certificate_id: Optional[str] = None
    certificate_issued_at: Optional[datetime] = None
    
    class Config:
        use_enum_values = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class Certificate(BaseModel):
    """Digital certificate model"""
    certificate_id: Optional[str] = None
    msme_id: str
    course_id: str
    
    # Certificate details
    certificate_number: str = Field(..., description="Unique certificate number")
    issued_at: datetime
    expires_at: Optional[datetime] = None
    
    # Achievement details
    final_score: float = Field(..., ge=0, le=100)
    completion_time_hours: float = Field(..., gt=0)
    
    # Certificate data
    certificate_url: Optional[HttpUrl] = None
    verification_code: str = Field(..., description="Code for certificate verification")
    
    # Metadata
    issued_by: str = Field(..., description="Issuing authority")
    is_valid: bool = Field(default=True)
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Request/Response models
class CourseEnrollRequest(BaseModel):
    course_id: str

class ModuleCompletionRequest(BaseModel):
    module_id: str
    time_spent_minutes: int = Field(..., gt=0)
    completion_data: Optional[Dict[str, Any]] = Field(default_factory=dict)

class QuizSubmissionRequest(BaseModel):
    module_id: str
    answers: Dict[str, str] = Field(..., description="question_id -> answer mapping")
    time_spent_minutes: int = Field(..., gt=0)

class QuizResult(BaseModel):
    quiz_id: str
    score: float = Field(..., ge=0, le=100)
    total_questions: int
    correct_answers: int
    time_spent_minutes: int
    passed: bool
    feedback: List[Dict[str, Any]] = Field(default_factory=list)
