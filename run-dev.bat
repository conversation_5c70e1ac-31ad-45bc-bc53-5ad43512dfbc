@echo off
setlocal enabledelayedexpansion

REM Credit Chakra Development Server Script for Windows
REM This script runs both the FastAPI backend and Next.js frontend simultaneously

echo 🚀 Starting Credit Chakra Development Environment...
echo ==================================================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

REM Check if npm is available
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm is not installed. Please install npm.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed
echo.

REM Install backend dependencies if needed
echo 📦 Checking backend dependencies...
cd backend

if not exist "venv" (
    echo 🔧 Creating Python virtual environment...
    python -m venv venv
)

echo 🔧 Activating virtual environment and installing dependencies...
call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1

if errorlevel 1 (
    echo ❌ Failed to install backend dependencies
    pause
    exit /b 1
) else (
    echo ✅ Backend dependencies installed
)

REM Start backend server
echo 🚀 Starting FastAPI backend server...
start "Credit Chakra Backend" cmd /k "venv\Scripts\activate.bat && python main.py"
echo ✅ Backend server started
echo 📍 Backend URL: http://localhost:8000
echo 📍 API Docs: http://localhost:8000/docs
cd ..

REM Install frontend dependencies if needed
echo.
echo 📦 Checking frontend dependencies...
cd msme-app

if not exist "node_modules" (
    echo 🔧 Installing frontend dependencies...
    npm install >nul 2>&1
    
    if errorlevel 1 (
        echo ❌ Failed to install frontend dependencies
        pause
        exit /b 1
    ) else (
        echo ✅ Frontend dependencies installed
    )
) else (
    echo ✅ Frontend dependencies already installed
)

REM Start frontend server
echo 🚀 Starting Next.js frontend server...
start "Credit Chakra Frontend" cmd /k "npm run dev"
echo ✅ Frontend server started
echo 📍 Frontend URL: http://localhost:3000
cd ..

echo.
echo 🎉 Development environment is ready!
echo ==================================================
echo 📱 Frontend (MSME App): http://localhost:3000
echo 🔧 Backend API: http://localhost:8000
echo 📚 API Documentation: http://localhost:8000/docs
echo ==================================================
echo.
echo 💡 Demo Login Credentials:
echo    Email: <EMAIL>
echo    Password: demo123
echo.
echo 🔄 Both servers are running in separate windows.
echo    Close the terminal windows to stop the servers.
echo.

pause
